<!--<?xml version="1.0" encoding="UTF-8" ?>-->
<!--<!DOCTYPE mapper-->
<!--        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"-->
<!--        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">-->
<!--<mapper namespace="com.ruoyi.hospital.mapper.AppointmentsMapper">-->

<!--    <resultMap type="com.ruoyi.hospital.domain.Appointments" id="AppointmentsResult">-->
<!--        <result property="appointmentId"    column="appointment_id"    />-->
<!--        <result property="patientName"    column="patient_name"    />-->
<!--        <result property="idCard"    column="id_card"    />-->
<!--        <result property="doctorName"    column="doctor_name"    />-->
<!--        <result property="hospitalName"    column="hospital_name"    />-->
<!--        <result property="deptName"    column="dept_name"    />-->
<!--        <result property="timeSlot"    column="time_slot"    />-->
<!--        <result property="cancelReason"    column="cancel_reason"    />-->
<!--        <result property="createTime"    column="create_time"    />-->
<!--        <result property="updateTime"    column="update_time"    />-->
<!--    </resultMap>-->

<!--    <sql id="selectAppointmentsVo">-->
<!--        select appointment_id, patient_name, id_card, doctor_name, hospital_name, dept_name, time_slot, cancel_reason, create_time, update_time from appointments-->
<!--    </sql>-->

<!--    <select id="selectAppointmentsList" parameterType="com.ruoyi.hospital.domain.Appointments" resultMap="AppointmentsResult">-->
<!--        <include refid="selectAppointmentsVo"/>-->
<!--        <where>-->
<!--            <if test="patientName != null  and patientName != ''"> and patient_name like concat('%', #{patientName}, '%')</if>-->
<!--            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>-->
<!--            <if test="doctorName != null  and doctorName != ''"> and doctor_name like concat('%', #{doctorName}, '%')</if>-->
<!--            <if test="hospitalName != null  and hospitalName != ''"> and hospital_name like concat('%', #{hospitalName}, '%')</if>-->
<!--            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>-->
<!--            <if test="timeSlot != null  and timeSlot != ''"> and time_slot = #{timeSlot}</if>-->
<!--            <if test="cancelReason != null  and cancelReason != ''"> and cancel_reason = #{cancelReason}</if>-->
<!--        </where>-->
<!--    </select>-->

<!--    <select id="selectAppointmentsByAppointmentId" parameterType="String" resultMap="AppointmentsResult">-->
<!--        <include refid="selectAppointmentsVo"/>-->
<!--        where appointment_id = #{appointmentId}-->
<!--    </select>-->

<!--    <insert id="insertAppointments" parameterType="com.ruoyi.hospital.domain.Appointments">-->
<!--        insert into appointments-->
<!--        <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--            <if test="appointmentId != null">appointment_id,</if>-->
<!--            <if test="patientName != null">patient_name,</if>-->
<!--            <if test="idCard != null">id_card,</if>-->
<!--            <if test="doctorName != null">doctor_name,</if>-->
<!--            <if test="hospitalName != null">hospital_name,</if>-->
<!--            <if test="deptName != null">dept_name,</if>-->
<!--            <if test="timeSlot != null and timeSlot != ''">time_slot,</if>-->
<!--            <if test="cancelReason != null">cancel_reason,</if>-->
<!--            <if test="createTime != null">create_time,</if>-->
<!--            <if test="updateTime != null">update_time,</if>-->
<!--        </trim>-->
<!--        <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--            <if test="appointmentId != null">#{appointmentId},</if>-->
<!--            <if test="patientName != null">#{patientName},</if>-->
<!--            <if test="idCard != null">#{idCard},</if>-->
<!--            <if test="doctorName != null">#{doctorName},</if>-->
<!--            <if test="hospitalName != null">#{hospitalName},</if>-->
<!--            <if test="deptName != null">#{deptName},</if>-->
<!--            <if test="timeSlot != null and timeSlot != ''">#{timeSlot},</if>-->
<!--            <if test="cancelReason != null">#{cancelReason},</if>-->
<!--            <if test="createTime != null">#{createTime},</if>-->
<!--            <if test="updateTime != null">#{updateTime},</if>-->
<!--        </trim>-->
<!--    </insert>-->

<!--    <update id="updateAppointments" parameterType="com.ruoyi.hospital.domain.Appointments">-->
<!--        update appointments-->
<!--        <trim prefix="SET" suffixOverrides=",">-->
<!--            <if test="patientName != null">patient_name = #{patientName},</if>-->
<!--            <if test="idCard != null">id_card = #{idCard},</if>-->
<!--            <if test="doctorName != null">doctor_name = #{doctorName},</if>-->
<!--            <if test="hospitalName != null">hospital_name = #{hospitalName},</if>-->
<!--            <if test="deptName != null">dept_name = #{deptName},</if>-->
<!--            <if test="timeSlot != null and timeSlot != ''">time_slot = #{timeSlot},</if>-->
<!--            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>-->
<!--            <if test="createTime != null">create_time = #{createTime},</if>-->
<!--            <if test="updateTime != null">update_time = #{updateTime},</if>-->
<!--        </trim>-->
<!--        where appointment_id = #{appointmentId}-->
<!--    </update>-->

<!--    <delete id="deleteAppointmentsByAppointmentId" parameterType="String">-->
<!--        delete from appointments where appointment_id = #{appointmentId}-->
<!--    </delete>-->

<!--    <delete id="deleteAppointmentsByAppointmentIds" parameterType="String">-->
<!--        delete from appointments where appointment_id in-->
<!--        <foreach item="appointmentId" collection="array" open="(" separator="," close=")">-->
<!--            #{appointmentId}-->
<!--        </foreach>-->
<!--    </delete>-->
<!--</mapper>-->