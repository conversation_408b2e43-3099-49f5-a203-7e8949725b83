<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.hospital.mapper.HospitalsMapper">

    <resultMap type="com.ruoyi.hospital.domain.Hospitals" id="HospitalsResult">
        <result property="hospitalId"    column="hospital_id"    />
        <result property="hospitalName"    column="hospital_name"    />
        <result property="hospitalLevel"    column="hospital_level"    />
        <result property="address"    column="address"    />
        <result property="phone"    column="phone"    />
        <result property="description"    column="description"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
    </resultMap>

    <sql id="selectHospitalsVo">
        select hospital_id, hospital_name, hospital_level, address, phone, description, create_time, update_time, deleted from hospitals
    </sql>

    <select id="selectHospitalsList" parameterType="com.ruoyi.hospital.domain.Hospitals" resultMap="HospitalsResult">
        <include refid="selectHospitalsVo"/>
        <where>
            <if test="hospitalName != null  and hospitalName != ''"> and hospital_name like concat('%', #{hospitalName}, '%')</if>
            <if test="hospitalLevel != null  and hospitalLevel != ''"> and hospital_level = #{hospitalLevel}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="deleted != null "> and deleted = #{deleted}</if>
        </where>
    </select>

    <select id="selectHospitalsByHospitalId" parameterType="Long" resultMap="HospitalsResult">
        <include refid="selectHospitalsVo"/>
        where hospital_id = #{hospitalId}
    </select>

    <insert id="insertHospitals" parameterType="com.ruoyi.hospital.domain.Hospitals" useGeneratedKeys="true" keyProperty="hospitalId">
        insert into hospitals
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hospitalName != null and hospitalName != ''">hospital_name,</if>
            <if test="hospitalLevel != null and hospitalLevel != ''">hospital_level,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="phone != null">phone,</if>
            <if test="description != null">description,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hospitalName != null and hospitalName != ''">#{hospitalName},</if>
            <if test="hospitalLevel != null and hospitalLevel != ''">#{hospitalLevel},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="phone != null">#{phone},</if>
            <if test="description != null">#{description},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <update id="updateHospitals" parameterType="com.ruoyi.hospital.domain.Hospitals">
        update hospitals
        <trim prefix="SET" suffixOverrides=",">
            <if test="hospitalName != null and hospitalName != ''">hospital_name = #{hospitalName},</if>
            <if test="hospitalLevel != null and hospitalLevel != ''">hospital_level = #{hospitalLevel},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where hospital_id = #{hospitalId}
    </update>

    <delete id="deleteHospitalsByHospitalId" parameterType="Long">
        delete from hospitals where hospital_id = #{hospitalId}
    </delete>

    <delete id="deleteHospitalsByHospitalIds" parameterType="String">
        delete from hospitals where hospital_id in
        <foreach item="hospitalId" collection="array" open="(" separator="," close=")">
            #{hospitalId}
        </foreach>
    </delete>
</mapper>