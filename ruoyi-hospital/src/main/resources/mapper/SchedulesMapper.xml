<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.hospital.mapper.SchedulesMapper">

    <resultMap type="com.ruoyi.hospital.domain.Schedules" id="SchedulesResult">
        <result property="scheduleId"    column="schedule_id"    />
        <result property="doctorId"    column="doctor_id"    />
        <result property="date"    column="date"    />
        <result property="morningStart"    column="morning_start"    />
        <result property="morningEnd"    column="morning_end"    />
        <result property="afternoonStart"    column="afternoon_start"    />
        <result property="afternoonEnd"    column="afternoon_end"    />
        <result property="nightStart"    column="night_start"    />
        <result property="nightEnd"    column="night_end"    />
        <result property="morningQuota"    column="morning_quota"    />
        <result property="afternoonQuota"    column="afternoon_quota"    />
        <result property="nightQuota"    column="night_quota"    />
        <result property="morningRemaining"    column="morning_remaining"    />
        <result property="afternoonRemaining"    column="afternoon_remaining"    />
        <result property="nightRemaining"    column="night_remaining"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
        <result property="doctorName" column="doctor_name"/>
        <result property="deptName" column="dept_name"/>
    </resultMap>

    <sql id="selectSchedulesVo">
        select schedule_id, doctor_id, date, morning_start, morning_end, afternoon_start, afternoon_end, night_start, night_end, morning_quota, afternoon_quota, night_quota, morning_remaining, afternoon_remaining, night_remaining, create_time, update_time, deleted from schedules
    </sql>

    <select id="selectSchedulesList" parameterType="Schedules" resultMap="SchedulesResult">
        select sc.schedule_id, sc.doctor_id, date, sc.morning_start, sc.morning_end, sc.afternoon_start, sc.afternoon_end,
        sc.night_start, sc.night_end, sc.morning_quota, sc.afternoon_quota, sc.night_quota,
        sc.morning_remaining, sc.afternoon_remaining, sc.night_remaining, sc.create_time, sc.update_time, sc.deleted,
        do.doctor_name, de.dept_name
        from schedules sc
        left join doctors do
        on sc.doctor_id = do.doctor_id
        left join departments de
        on do.dept_id = de.dept_id
        <where>
            <if test="doctorName != null">
                and do.doctor_name = #{doctorName}
            </if>
        </where>
    </select>

<!--    <select id="selectSchedulesList" parameterType="com.ruoyi.hospital.domain.Schedules" resultMap="SchedulesResult">-->
<!--        <include refid="selectSchedulesVo"/>-->
<!--        <where>-->
<!--            <if test="doctorId != null "> and doctor_id = #{doctorId}</if>-->
<!--            <if test="doctorName != null "> and doctorName = #{doctorName}</if>-->
<!--            <if test="deptName != null "> and deptName = #{deptName}</if>-->
<!--            <if test="date != null  and date != ''"> and date = #{date}</if>-->
<!--            <if test="morningStart != null "> and morning_start = #{morningStart}</if>-->
<!--            <if test="morningEnd != null "> and morning_end = #{morningEnd}</if>-->
<!--            <if test="afternoonStart != null "> and afternoon_start = #{afternoonStart}</if>-->
<!--            <if test="afternoonEnd != null "> and afternoon_end = #{afternoonEnd}</if>-->
<!--            <if test="nightStart != null "> and night_start = #{nightStart}</if>-->
<!--            <if test="nightEnd != null "> and night_end = #{nightEnd}</if>-->
<!--            <if test="morningQuota != null "> and morning_quota = #{morningQuota}</if>-->
<!--            <if test="afternoonQuota != null "> and afternoon_quota = #{afternoonQuota}</if>-->
<!--            <if test="nightQuota != null "> and night_quota = #{nightQuota}</if>-->
<!--            <if test="morningRemaining != null "> and morning_remaining = #{morningRemaining}</if>-->
<!--            <if test="afternoonRemaining != null "> and afternoon_remaining = #{afternoonRemaining}</if>-->
<!--            <if test="nightRemaining != null "> and night_remaining = #{nightRemaining}</if>-->
<!--            <if test="deleted != null "> and deleted = #{deleted}</if>-->
<!--        </where>-->
<!--    </select>-->

    <select id="selectSchedulesByScheduleId" parameterType="Long" resultMap="SchedulesResult">
        <include refid="selectSchedulesVo"/>
        where schedule_id = #{scheduleId}
    </select>

    <insert id="insertSchedules" parameterType="com.ruoyi.hospital.domain.Schedules" useGeneratedKeys="true" keyProperty="scheduleId">
        insert into schedules
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="doctorId != null">doctor_id,</if>
            <if test="date != null and date != ''">date,</if>
            <if test="morningStart != null">morning_start,</if>
            <if test="morningEnd != null">morning_end,</if>
            <if test="afternoonStart != null">afternoon_start,</if>
            <if test="afternoonEnd != null">afternoon_end,</if>
            <if test="nightStart != null">night_start,</if>
            <if test="nightEnd != null">night_end,</if>
            <if test="morningQuota != null">morning_quota,</if>
            <if test="afternoonQuota != null">afternoon_quota,</if>
            <if test="nightQuota != null">night_quota,</if>
            <if test="morningRemaining != null">morning_remaining,</if>
            <if test="afternoonRemaining != null">afternoon_remaining,</if>
            <if test="nightRemaining != null">night_remaining,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="doctorId != null">#{doctorId},</if>
            <if test="date != null and date != ''">#{date},</if>
            <if test="morningStart != null">#{morningStart},</if>
            <if test="morningEnd != null">#{morningEnd},</if>
            <if test="afternoonStart != null">#{afternoonStart},</if>
            <if test="afternoonEnd != null">#{afternoonEnd},</if>
            <if test="nightStart != null">#{nightStart},</if>
            <if test="nightEnd != null">#{nightEnd},</if>
            <if test="morningQuota != null">#{morningQuota},</if>
            <if test="afternoonQuota != null">#{afternoonQuota},</if>
            <if test="nightQuota != null">#{nightQuota},</if>
            <if test="morningRemaining != null">#{morningRemaining},</if>
            <if test="afternoonRemaining != null">#{afternoonRemaining},</if>
            <if test="nightRemaining != null">#{nightRemaining},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <update id="updateSchedules" parameterType="com.ruoyi.hospital.domain.Schedules">
        update schedules
        <trim prefix="SET" suffixOverrides=",">
            <if test="doctorId != null">doctor_id = #{doctorId},</if>
            <if test="date != null and date != ''">date = #{date},</if>
            <if test="morningStart != null">morning_start = #{morningStart},</if>
            <if test="morningEnd != null">morning_end = #{morningEnd},</if>
            <if test="afternoonStart != null">afternoon_start = #{afternoonStart},</if>
            <if test="afternoonEnd != null">afternoon_end = #{afternoonEnd},</if>
            <if test="nightStart != null">night_start = #{nightStart},</if>
            <if test="nightEnd != null">night_end = #{nightEnd},</if>
            <if test="morningQuota != null">morning_quota = #{morningQuota},</if>
            <if test="afternoonQuota != null">afternoon_quota = #{afternoonQuota},</if>
            <if test="nightQuota != null">night_quota = #{nightQuota},</if>
            <if test="morningRemaining != null">morning_remaining = #{morningRemaining},</if>
            <if test="afternoonRemaining != null">afternoon_remaining = #{afternoonRemaining},</if>
            <if test="nightRemaining != null">night_remaining = #{nightRemaining},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where schedule_id = #{scheduleId}
    </update>

    <delete id="deleteSchedulesByScheduleId" parameterType="Long">
        delete from schedules where schedule_id = #{scheduleId}
    </delete>

    <delete id="deleteSchedulesByScheduleIds" parameterType="String">
        delete from schedules where schedule_id in
        <foreach item="scheduleId" collection="array" open="(" separator="," close=")">
            #{scheduleId}
        </foreach>
    </delete>
</mapper>