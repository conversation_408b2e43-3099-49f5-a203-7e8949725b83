<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.hospital.mapper.DoctorsMapper">

    <resultMap type="com.ruoyi.hospital.domain.Doctors" id="DoctorsResult">
        <result property="doctorId"    column="doctor_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="title"    column="title"    />
        <result property="specialty"    column="specialty"    />
        <result property="workYears"    column="work_years"    />
        <result property="consultationFee"    column="consultation_fee"    />
        <result property="introduction"    column="introduction"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
        <result property="doctorName"    column="doctor_name"    />
        <result property="deptName"    column="dept_name"    />
    </resultMap>

    <sql id="selectDoctorsVo">
        select doctor_id, doctor_name, dept_id, title, specialty, work_years, consultation_fee, introduction, create_time, update_time, deleted from doctors
    </sql>

    <select id="selectDoctorsList" parameterType="com.ruoyi.hospital.domain.Doctors" resultMap="DoctorsResult">
        SELECT
        d.*,
        dept.dept_name
        FROM
        doctors d
        LEFT JOIN
        departments dept ON d.dept_id = dept.dept_id
        <where>
            <if test="deptId != null"> AND d.dept_id = #{deptId}</if>
            <if test="doctorName != null"> AND d.doctor_name = #{doctorName}</if>
            <if test="title != null and title != ''"> AND d.title = #{title}</if>
            <if test="specialty != null and specialty != ''"> AND d.specialty = #{specialty}</if>
            <if test="workYears != null"> AND d.work_years = #{workYears}</if>
            <if test="consultationFee != null"> AND d.consultation_fee = #{consultationFee}</if>
            <if test="introduction != null and introduction != ''"> AND d.introduction = #{introduction}</if>
            <if test="deleted != null"> AND d.deleted = #{deleted}</if>
        </where>
    </select>

    <select id="selectDoctorsByDoctorId" parameterType="Long" resultMap="DoctorsResult">
        <include refid="selectDoctorsVo"/>
        where doctor_id = #{doctorId}
    </select>

    <insert id="insertDoctors" parameterType="com.ruoyi.hospital.domain.Doctors" useGeneratedKeys="true" keyProperty="doctorId">
        insert into doctors
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="doctorName != null">doctor_name,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="specialty != null">specialty,</if>
            <if test="workYears != null">work_years,</if>
            <if test="consultationFee != null">consultation_fee,</if>
            <if test="introduction != null">introduction,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="doctorName != null">#{doctorName},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="specialty != null">#{specialty},</if>
            <if test="workYears != null">#{workYears},</if>
            <if test="consultationFee != null">#{consultationFee},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <update id="updateDoctors" parameterType="com.ruoyi.hospital.domain.Doctors">
        update doctors
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="specialty != null">specialty = #{specialty},</if>
            <if test="doctorName != null">doctor_name = #{doctorName},</if>
            <if test="workYears != null">work_years = #{workYears},</if>
            <if test="consultationFee != null">consultation_fee = #{consultationFee},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where doctor_id = #{doctorId}
    </update>

    <delete id="deleteDoctorsByDoctorId" parameterType="Long">
        delete from doctors where doctor_id = #{doctorId}
    </delete>

    <delete id="deleteDoctorsByDoctorIds" parameterType="String">
        delete from doctors where doctor_id in
        <foreach item="doctorId" collection="array" open="(" separator="," close=")">
            #{doctorId}
        </foreach>
    </delete>
</mapper>