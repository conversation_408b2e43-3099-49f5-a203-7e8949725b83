<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.hospital.mapper.MedicalRecordsMapper">

    <resultMap type="com.ruoyi.hospital.domain.MedicalRecords" id="MedicalRecordsResult">
        <result property="recordId"    column="record_id"    />
        <result property="appointmentId"    column="appointment_id"    />
        <result property="patientName"    column="patient_name"    />
        <result property="doctorId"    column="doctor_id"    />
        <result property="diagnosis"    column="diagnosis"    />
        <result property="treatmentPlan"    column="treatment_plan"    />
        <result property="prescription"    column="prescription"    />
        <result property="examinationItems"    column="examination_items"    />
        <result property="visitTime"    column="visit_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectMedicalRecordsVo">
        select record_id, appointment_id, patient_name, doctor_id, diagnosis, treatment_plan, prescription, examination_items, visit_time, create_time from medical_records
    </sql>

    <select id="selectMedicalRecordsList" parameterType="com.ruoyi.hospital.domain.MedicalRecords" resultMap="MedicalRecordsResult">
        <include refid="selectMedicalRecordsVo"/>
        <where>
            <if test="appointmentId != null  and appointmentId != ''"> and appointment_id = #{appointmentId}</if>
            <if test="patientName != null "> and patient_name like concat('%', #{patientName}, '%')</if>
            <if test="doctorId != null "> and doctor_id = #{doctorId}</if>
            <if test="diagnosis != null  and diagnosis != ''"> and diagnosis = #{diagnosis}</if>
            <if test="treatmentPlan != null  and treatmentPlan != ''"> and treatment_plan = #{treatmentPlan}</if>
            <if test="prescription != null  and prescription != ''"> and prescription = #{prescription}</if>
            <if test="examinationItems != null  and examinationItems != ''"> and examination_items = #{examinationItems}</if>
            <if test="visitTime != null "> and visit_time = #{visitTime}</if>
        </where>
    </select>

    <select id="selectMedicalRecordsByRecordId" parameterType="Long" resultMap="MedicalRecordsResult">
        <include refid="selectMedicalRecordsVo"/>
        where record_id = #{recordId}
    </select>

    <insert id="insertMedicalRecords" parameterType="com.ruoyi.hospital.domain.MedicalRecords" useGeneratedKeys="true" keyProperty="recordId">
        insert into medical_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appointmentId != null and appointmentId != ''">appointment_id,</if>
            <if test="patientName != null">patient_name,</if>
            <if test="doctorId != null">doctor_id,</if>
            <if test="diagnosis != null">diagnosis,</if>
            <if test="treatmentPlan != null">treatment_plan,</if>
            <if test="prescription != null">prescription,</if>
            <if test="examinationItems != null">examination_items,</if>
            <if test="visitTime != null">visit_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appointmentId != null and appointmentId != ''">#{appointmentId},</if>
            <if test="patientName != null">#{patientName},</if>
            <if test="doctorId != null">#{doctorId},</if>
            <if test="diagnosis != null">#{diagnosis},</if>
            <if test="treatmentPlan != null">#{treatmentPlan},</if>
            <if test="prescription != null">#{prescription},</if>
            <if test="examinationItems != null">#{examinationItems},</if>
            <if test="visitTime != null">#{visitTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateMedicalRecords" parameterType="com.ruoyi.hospital.domain.MedicalRecords">
        update medical_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="appointmentId != null and appointmentId != ''">appointment_id = #{appointmentId},</if>
            <if test="patientName != null">patient_name = #{patientName},</if>
            <if test="doctorId != null">doctor_id = #{doctorId},</if>
            <if test="diagnosis != null">diagnosis = #{diagnosis},</if>
            <if test="treatmentPlan != null">treatment_plan = #{treatmentPlan},</if>
            <if test="prescription != null">prescription = #{prescription},</if>
            <if test="examinationItems != null">examination_items = #{examinationItems},</if>
            <if test="visitTime != null">visit_time = #{visitTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteMedicalRecordsByRecordId" parameterType="Long">
        delete from medical_records where record_id = #{recordId}
    </delete>

    <delete id="deleteMedicalRecordsByRecordIds" parameterType="String">
        delete from medical_records where record_id in
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>