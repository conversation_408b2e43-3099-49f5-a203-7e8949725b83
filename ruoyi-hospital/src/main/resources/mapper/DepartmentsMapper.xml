<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.hospital.mapper.DepartmentsMapper">

    <resultMap type="com.ruoyi.hospital.domain.Departments" id="DepartmentsResult">
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="hospitalId"    column="hospital_id"    />
        <result property="description"    column="description"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
        <result property="hospitalName"    column="hospital_name"    />
    </resultMap>

    <sql id="selectDepartmentsVo">
        select dept_id, dept_name, hospital_id, description, create_time, update_time, deleted from departments
    </sql>

    <select id="selectDepartmentsList" parameterType="com.ruoyi.hospital.domain.Departments" resultMap="DepartmentsResult">
        SELECT
        d.*,
        h.hospital_name  <!-- 新增字段 -->
        FROM
        departments d
        LEFT JOIN
        hospitals h ON d.hospital_id = h.hospital_id  <!-- 关联医院表 -->
        <where>
            <if test="deptName != null and deptName != ''">
                AND d.dept_name LIKE CONCAT('%', #{deptName}, '%')
            </if>
            <if test="hospitalId != null">
                AND d.hospital_id = #{hospitalId}
            </if>
            <if test="description != null and description != ''">
                AND d.description = #{description}
            </if>
            <if test="deleted != null">
                AND d.deleted = #{deleted}
            </if>
        </where>
    </select>

<!--    <select id="selectDepartmentsList" parameterType="com.ruoyi.hospital.domain.Departments" resultMap="DepartmentsResult">-->
<!--        <where>-->
<!--            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>-->
<!--            <if test="hospitalId != null "> and hospital_id = #{hospitalId}</if>-->
<!--            <if test="description != null  and description != ''"> and description = #{description}</if>-->
<!--            <if test="deleted != null "> and deleted = #{deleted}</if>-->
<!--        </where>-->
<!--    </select>-->

    <select id="selectDepartmentsByDeptId" parameterType="Long" resultMap="DepartmentsResult">
        <include refid="selectDepartmentsVo"/>
        where dept_id = #{deptId}
    </select>

    <insert id="insertDepartments" parameterType="com.ruoyi.hospital.domain.Departments" useGeneratedKeys="true" keyProperty="deptId">
        insert into departments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptName != null and deptName != ''">dept_name,</if>
            <if test="hospitalId != null">hospital_id,</if>
            <if test="description != null">description,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptName != null and deptName != ''">#{deptName},</if>
            <if test="hospitalId != null">#{hospitalId},</if>
            <if test="description != null">#{description},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
        </trim>
    </insert>

    <update id="updateDepartments" parameterType="Departments">
        update departments
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="hospitalId != null">hospital_id = #{hospitalId},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where dept_id = #{deptId}
    </update>

    <delete id="deleteDepartmentsByDeptId" parameterType="Long">
        delete from departments where dept_id = #{deptId}
    </delete>

    <delete id="deleteDepartmentsByDeptIds" parameterType="String">
        delete from departments where dept_id in
        <foreach item="deptId" collection="array" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </delete>
</mapper>