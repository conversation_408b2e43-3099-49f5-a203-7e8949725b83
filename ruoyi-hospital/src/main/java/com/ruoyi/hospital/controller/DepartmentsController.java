package com.ruoyi.hospital.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.hospital.domain.Departments;
import com.ruoyi.hospital.service.IDepartmentsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 科室信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/system/departments")
public class DepartmentsController extends BaseController
{
    @Autowired
    private IDepartmentsService departmentsService;

    /**
     * 查询科室信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:departments:list')")
    @GetMapping("/list")
    public TableDataInfo list(Departments departments)
    {
        startPage();
        List<Departments> list = departmentsService.selectDepartmentsList(departments);
        return getDataTable(list);
    }

    /**
     * 导出科室信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:departments:export')")
    @Log(title = "科室信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Departments departments)
    {
        List<Departments> list = departmentsService.selectDepartmentsList(departments);
        ExcelUtil<Departments> util = new ExcelUtil<Departments>(Departments.class);
        util.exportExcel(response, list, "科室信息数据");
    }

    /**
     * 获取科室信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:departments:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable("deptId") Long deptId)
    {
        return success(departmentsService.selectDepartmentsByDeptId(deptId));
    }

    /**
     * 新增科室信息
     */
    @PreAuthorize("@ss.hasPermi('system:departments:add')")
    @Log(title = "科室信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Departments departments)
    {
        return toAjax(departmentsService.insertDepartments(departments));
    }

    /**
     * 修改科室信息
     */
    @PreAuthorize("@ss.hasPermi('system:departments:edit')")
    @Log(title = "科室信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Departments departments)
    {
        return toAjax(departmentsService.updateDepartments(departments));
    }

    /**
     * 删除科室信息
     */
    @PreAuthorize("@ss.hasPermi('system:departments:remove')")
    @Log(title = "科室信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{deptIds}")
    public AjaxResult remove(@PathVariable Long[] deptIds)
    {
        return toAjax(departmentsService.deleteDepartmentsByDeptIds(deptIds));
    }
}
