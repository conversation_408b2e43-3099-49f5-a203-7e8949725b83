package com.ruoyi.hospital.controller;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.hospital.domain.Appointments;
import com.ruoyi.hospital.service.IAppointmentsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 预约订单Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/system/appointments")
public class AppointmentsController extends BaseController
{
    @Resource
    private IAppointmentsService appointmentsService;

    /**
     * 查询预约订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:appointments:list')")
    @GetMapping("/list")
    public TableDataInfo list(Appointments appointments)
    {
//        System.out.println("sdsfsfrse");
        startPage();
        List<Appointments> list = appointmentsService.selectAppointmentsList(appointments);
        return getDataTable(list);
    }

    /**
     * 导出预约订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:appointments:export')")
    @Log(title = "预约订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Appointments appointments)
    {
        List<Appointments> list = appointmentsService.selectAppointmentsList(appointments);
        ExcelUtil<Appointments> util = new ExcelUtil<Appointments>(Appointments.class);
        util.exportExcel(response, list, "预约订单数据");
    }

    /**
     * 获取预约订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:appointments:query')")
    @GetMapping(value = "/{appointmentId}")
    public AjaxResult getInfo(@PathVariable("appointmentId") String appointmentId)
    {
        return success(appointmentsService.selectAppointmentsByAppointmentId(appointmentId));
    }

    /**
     * 新增预约订单
     */
    @PreAuthorize("@ss.hasPermi('system:appointments:add')")
    @Log(title = "预约订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Appointments appointments)
    {
        System.out.println(appointments);
        return toAjax(appointmentsService.insertAppointments(appointments));
    }

    /**
     * 修改预约订单
     */
    @PreAuthorize("@ss.hasPermi('system:appointments:edit')")
    @Log(title = "预约订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Appointments appointments)
    {
        return toAjax(appointmentsService.updateAppointments(appointments));
    }

    /**
     * 删除预约订单
     */
    @PreAuthorize("@ss.hasPermi('system:appointments:remove')")
    @Log(title = "预约订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{appointmentIds}")
    public AjaxResult remove(@PathVariable String[] appointmentIds)
    {
        return toAjax(appointmentsService.deleteAppointmentsByAppointmentIds(appointmentIds));
    }
}
