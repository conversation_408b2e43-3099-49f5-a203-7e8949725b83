package com.ruoyi.hospital.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.hospital.domain.Doctors;
import com.ruoyi.hospital.service.IDoctorsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 医生信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/system/doctors")
public class DoctorsController extends BaseController
{
    @Autowired
    private IDoctorsService doctorsService;

    /**
     * 查询医生信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:doctors:list')")
    @GetMapping("/list")
    public TableDataInfo list(Doctors doctors)
    {
        startPage();
        List<Doctors> list = doctorsService.selectDoctorsList(doctors);
        return getDataTable(list);
    }

    /**
     * 导出医生信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:doctors:export')")
    @Log(title = "医生信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Doctors doctors)
    {
        List<Doctors> list = doctorsService.selectDoctorsList(doctors);
        ExcelUtil<Doctors> util = new ExcelUtil<Doctors>(Doctors.class);
        util.exportExcel(response, list, "医生信息数据");
    }

    /**
     * 获取医生信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:doctors:query')")
    @GetMapping(value = "/{doctorId}")
    public AjaxResult getInfo(@PathVariable("doctorId") Long doctorId)
    {
        return success(doctorsService.selectDoctorsByDoctorId(doctorId));
    }

    /**
     * 新增医生信息
     */
    @PreAuthorize("@ss.hasPermi('system:doctors:add')")
    @Log(title = "医生信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Doctors doctors)
    {
        return toAjax(doctorsService.insertDoctors(doctors));
    }

    /**
     * 修改医生信息
     */
    @PreAuthorize("@ss.hasPermi('system:doctors:edit')")
    @Log(title = "医生信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Doctors doctors)
    {
        return toAjax(doctorsService.updateDoctors(doctors));
    }

    /**
     * 删除医生信息
     */
    @PreAuthorize("@ss.hasPermi('system:doctors:remove')")
    @Log(title = "医生信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{doctorIds}")
    public AjaxResult remove(@PathVariable Long[] doctorIds)
    {
        return toAjax(doctorsService.deleteDoctorsByDoctorIds(doctorIds));
    }
}
