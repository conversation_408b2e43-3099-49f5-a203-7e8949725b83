package com.ruoyi.hospital.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.hospital.domain.Hospitals;
import com.ruoyi.hospital.service.IHospitalsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 医院信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/system/hospitals")
public class HospitalsController extends BaseController
{
    @Autowired
    private IHospitalsService hospitalsService;

    /**
     * 查询医院信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:hospitals:list')")
    @GetMapping("/list")
    public TableDataInfo list(Hospitals hospitals)
    {
        startPage();
        List<Hospitals> list = hospitalsService.selectHospitalsList(hospitals);
        return getDataTable(list);
    }

    /**
     * 导出医院信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:hospitals:export')")
    @Log(title = "医院信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Hospitals hospitals)
    {
        List<Hospitals> list = hospitalsService.selectHospitalsList(hospitals);
        ExcelUtil<Hospitals> util = new ExcelUtil<Hospitals>(Hospitals.class);
        util.exportExcel(response, list, "医院信息数据");
    }

    /**
     * 获取医院信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:hospitals:query')")
    @GetMapping(value = "/{hospitalId}")
    public AjaxResult getInfo(@PathVariable("hospitalId") Long hospitalId)
    {
        return success(hospitalsService.selectHospitalsByHospitalId(hospitalId));
    }

    /**
     * 新增医院信息
     */
    @PreAuthorize("@ss.hasPermi('system:hospitals:add')")
    @Log(title = "医院信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Hospitals hospitals)
    {
        return toAjax(hospitalsService.insertHospitals(hospitals));
    }

    /**
     * 修改医院信息
     */
    @PreAuthorize("@ss.hasPermi('system:hospitals:edit')")
    @Log(title = "医院信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Hospitals hospitals)
    {
        return toAjax(hospitalsService.updateHospitals(hospitals));
    }

    /**
     * 删除医院信息
     */
    @PreAuthorize("@ss.hasPermi('system:hospitals:remove')")
    @Log(title = "医院信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{hospitalIds}")
    public AjaxResult remove(@PathVariable Long[] hospitalIds)
    {
        return toAjax(hospitalsService.deleteHospitalsByHospitalIds(hospitalIds));
    }
}
