package com.ruoyi.hospital.mapper;

import com.ruoyi.hospital.domain.Hospitals;

import java.util.List;


/**
 * 医院信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface HospitalsMapper
{
    /**
     * 查询医院信息
     *
     * @param hospitalId 医院信息主键
     * @return 医院信息
     */
    public Hospitals selectHospitalsByHospitalId(Long hospitalId);

    /**
     * 查询医院信息列表
     *
     * @param hospitals 医院信息
     * @return 医院信息集合
     */
    public List<Hospitals> selectHospitalsList(Hospitals hospitals);

    /**
     * 新增医院信息
     *
     * @param hospitals 医院信息
     * @return 结果
     */
    public int insertHospitals(Hospitals hospitals);

    /**
     * 修改医院信息
     *
     * @param hospitals 医院信息
     * @return 结果
     */
    public int updateHospitals(Hospitals hospitals);

    /**
     * 删除医院信息
     *
     * @param hospitalId 医院信息主键
     * @return 结果
     */
    public int deleteHospitalsByHospitalId(Long hospitalId);

    /**
     * 批量删除医院信息
     *
     * @param hospitalIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHospitalsByHospitalIds(Long[] hospitalIds);
}
