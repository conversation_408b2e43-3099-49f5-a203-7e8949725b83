package com.ruoyi.hospital.service;

import com.ruoyi.hospital.domain.Schedules;

import java.util.List;

/**
 * 医生排班Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface ISchedulesService 
{
    /**
     * 查询医生排班
     * 
     * @param scheduleId 医生排班主键
     * @return 医生排班
     */
    public Schedules selectSchedulesByScheduleId(Long scheduleId);

    /**
     * 查询医生排班列表
     * 
     * @param schedules 医生排班
     * @return 医生排班集合
     */
    public List<Schedules> selectSchedulesList(Schedules schedules);

    /**
     * 新增医生排班
     * 
     * @param schedules 医生排班
     * @return 结果
     */
    public int insertSchedules(Schedules schedules);

    /**
     * 修改医生排班
     * 
     * @param schedules 医生排班
     * @return 结果
     */
    public int updateSchedules(Schedules schedules);

    /**
     * 批量删除医生排班
     * 
     * @param scheduleIds 需要删除的医生排班主键集合
     * @return 结果
     */
    public int deleteSchedulesByScheduleIds(Long[] scheduleIds);

    /**
     * 删除医生排班信息
     * 
     * @param scheduleId 医生排班主键
     * @return 结果
     */
    public int deleteSchedulesByScheduleId(Long scheduleId);
}
