package com.ruoyi.hospital.service;

import com.ruoyi.hospital.domain.Departments;

import java.util.List;

/**
 * 科室信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IDepartmentsService 
{
    /**
     * 查询科室信息
     * 
     * @param deptId 科室信息主键
     * @return 科室信息
     */
    public Departments selectDepartmentsByDeptId(Long deptId);

    /**
     * 查询科室信息列表
     * 
     * @param departments 科室信息
     * @return 科室信息集合
     */
    public List<Departments> selectDepartmentsList(Departments departments);

    /**
     * 新增科室信息
     * 
     * @param departments 科室信息
     * @return 结果
     */
    public int insertDepartments(Departments departments);

    /**
     * 修改科室信息
     * 
     * @param departments 科室信息
     * @return 结果
     */
    public int updateDepartments(Departments departments);

    /**
     * 批量删除科室信息
     * 
     * @param deptIds 需要删除的科室信息主键集合
     * @return 结果
     */
    public int deleteDepartmentsByDeptIds(Long[] deptIds);

    /**
     * 删除科室信息信息
     * 
     * @param deptId 科室信息主键
     * @return 结果
     */
    public int deleteDepartmentsByDeptId(Long deptId);
}
