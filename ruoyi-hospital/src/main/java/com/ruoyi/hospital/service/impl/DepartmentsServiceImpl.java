package com.ruoyi.hospital.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.hospital.domain.Departments;
import com.ruoyi.hospital.mapper.DepartmentsMapper;
import com.ruoyi.hospital.service.IDepartmentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 科室信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class DepartmentsServiceImpl implements IDepartmentsService
{
    @Autowired
    private DepartmentsMapper departmentsMapper;

    /**
     * 查询科室信息
     * 
     * @param deptId 科室信息主键
     * @return 科室信息
     */
    @Override
    public Departments selectDepartmentsByDeptId(Long deptId)
    {
        return departmentsMapper.selectDepartmentsByDeptId(deptId);
    }

    /**
     * 查询科室信息列表
     * 
     * @param departments 科室信息
     * @return 科室信息
     */
    @Override
    public List<Departments> selectDepartmentsList(Departments departments)
    {
        return departmentsMapper.selectDepartmentsList(departments);
    }

    /**
     * 新增科室信息
     * 
     * @param departments 科室信息
     * @return 结果
     */
    @Override
    public int insertDepartments(Departments departments)
    {
        departments.setCreateTime(DateUtils.getNowDate());
        return departmentsMapper.insertDepartments(departments);
    }

    /**
     * 修改科室信息
     * 
     * @param departments 科室信息
     * @return 结果
     */
    @Override
    public int updateDepartments(Departments departments)
    {
        departments.setUpdateTime(DateUtils.getNowDate());
        return departmentsMapper.updateDepartments(departments);
    }

    /**
     * 批量删除科室信息
     * 
     * @param deptIds 需要删除的科室信息主键
     * @return 结果
     */
    @Override
    public int deleteDepartmentsByDeptIds(Long[] deptIds)
    {
        return departmentsMapper.deleteDepartmentsByDeptIds(deptIds);
    }

    /**
     * 删除科室信息信息
     * 
     * @param deptId 科室信息主键
     * @return 结果
     */
    @Override
    public int deleteDepartmentsByDeptId(Long deptId)
    {
        return departmentsMapper.deleteDepartmentsByDeptId(deptId);
    }
}
