package com.ruoyi.hospital.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.hospital.domain.Appointments;
import com.ruoyi.hospital.mapper.AppointmentsMapper;
import com.ruoyi.hospital.service.IAppointmentsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 预约订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class AppointmentsServiceImpl implements IAppointmentsService
{
    @Resource
    private AppointmentsMapper appointmentsMapper;

    /**
     * 查询预约订单
     * 
     * @param appointmentId 预约订单主键
     * @return 预约订单
     */
    @Override
    public Appointments selectAppointmentsByAppointmentId(String appointmentId)
    {
        return appointmentsMapper.selectAppointmentsByAppointmentId(appointmentId);
    }

    /**
     * 查询预约订单列表
     * 
     * @param appointments 预约订单
     * @return 预约订单
     */
    @Override
    public List<Appointments> selectAppointmentsList(Appointments appointments)
    {
        return appointmentsMapper.selectAppointmentsList(appointments);
    }

    /**
     * 新增预约订单
     * 
     * @param appointments 预约订单
     * @return 结果
     */
    @Override
    public int insertAppointments(Appointments appointments)
    {
        appointments.setAppointmentId(UUID.randomUUID().toString());
        appointments.setCreateTime(DateUtils.getNowDate());
        return appointmentsMapper.insertAppointments(appointments);
    }

    /**
     * 修改预约订单
     * 
     * @param appointments 预约订单
     * @return 结果
     */
    @Override
    public int updateAppointments(Appointments appointments)
    {
        appointments.setUpdateTime(DateUtils.getNowDate());
        return appointmentsMapper.updateAppointments(appointments);
    }

    /**
     * 批量删除预约订单
     * 
     * @param appointmentIds 需要删除的预约订单主键
     * @return 结果
     */
    @Override
    public int deleteAppointmentsByAppointmentIds(String[] appointmentIds)
    {
        return appointmentsMapper.deleteAppointmentsByAppointmentIds(appointmentIds);
    }

    /**
     * 删除预约订单信息
     * 
     * @param appointmentId 预约订单主键
     * @return 结果
     */
    @Override
    public int deleteAppointmentsByAppointmentId(String appointmentId)
    {
        return appointmentsMapper.deleteAppointmentsByAppointmentId(appointmentId);
    }
}
