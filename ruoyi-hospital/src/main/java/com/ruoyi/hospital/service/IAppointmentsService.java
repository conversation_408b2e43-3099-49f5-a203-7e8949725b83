package com.ruoyi.hospital.service;

import com.ruoyi.hospital.domain.Appointments;

import java.util.List;

/**
 * 预约订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IAppointmentsService 
{
    /**
     * 查询预约订单
     * 
     * @param appointmentId 预约订单主键
     * @return 预约订单
     */
    public Appointments selectAppointmentsByAppointmentId(String appointmentId);

    /**
     * 查询预约订单列表
     * 
     * @param appointments 预约订单
     * @return 预约订单集合
     */
    public List<Appointments> selectAppointmentsList(Appointments appointments);

    /**
     * 新增预约订单
     * 
     * @param appointments 预约订单
     * @return 结果
     */
    public int insertAppointments(Appointments appointments);

    /**
     * 修改预约订单
     * 
     * @param appointments 预约订单
     * @return 结果
     */
    public int updateAppointments(Appointments appointments);

    /**
     * 批量删除预约订单
     * 
     * @param appointmentIds 需要删除的预约订单主键集合
     * @return 结果
     */
    public int deleteAppointmentsByAppointmentIds(String[] appointmentIds);

    /**
     * 删除预约订单信息
     * 
     * @param appointmentId 预约订单主键
     * @return 结果
     */
    public int deleteAppointmentsByAppointmentId(String appointmentId);
}
