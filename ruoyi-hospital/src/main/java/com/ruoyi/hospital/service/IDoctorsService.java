package com.ruoyi.hospital.service;

import com.ruoyi.hospital.domain.Doctors;

import java.util.List;

/**
 * 医生信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IDoctorsService 
{
    /**
     * 查询医生信息
     * 
     * @param doctorId 医生信息主键
     * @return 医生信息
     */
    public Doctors selectDoctorsByDoctorId(Long doctorId);

    /**
     * 查询医生信息列表
     * 
     * @param doctors 医生信息
     * @return 医生信息集合
     */
    public List<Doctors> selectDoctorsList(Doctors doctors);

    /**
     * 新增医生信息
     * 
     * @param doctors 医生信息
     * @return 结果
     */
    public int insertDoctors(Doctors doctors);

    /**
     * 修改医生信息
     * 
     * @param doctors 医生信息
     * @return 结果
     */
    public int updateDoctors(Doctors doctors);

    /**
     * 批量删除医生信息
     * 
     * @param doctorIds 需要删除的医生信息主键集合
     * @return 结果
     */
    public int deleteDoctorsByDoctorIds(Long[] doctorIds);

    /**
     * 删除医生信息信息
     * 
     * @param doctorId 医生信息主键
     * @return 结果
     */
    public int deleteDoctorsByDoctorId(Long doctorId);
}
