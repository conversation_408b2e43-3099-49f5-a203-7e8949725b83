package com.ruoyi.hospital.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.hospital.domain.Doctors;
import com.ruoyi.hospital.mapper.DoctorsMapper;
import com.ruoyi.hospital.service.IDoctorsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 医生信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class DoctorsServiceImpl implements IDoctorsService
{
    @Resource
    private DoctorsMapper doctorsMapper;

    /**
     * 查询医生信息
     * 
     * @param doctorId 医生信息主键
     * @return 医生信息
     */
    @Override
    public Doctors selectDoctorsByDoctorId(Long doctorId)
    {
        return doctorsMapper.selectDoctorsByDoctorId(doctorId);
    }

    /**
     * 查询医生信息列表
     * 
     * @param doctors 医生信息
     * @return 医生信息
     */
    @Override
    public List<Doctors> selectDoctorsList(Doctors doctors)
    {
        return doctorsMapper.selectDoctorsList(doctors);
    }

    /**
     * 新增医生信息
     * 
     * @param doctors 医生信息
     * @return 结果
     */
    @Override
    public int insertDoctors(Doctors doctors)
    {
        doctors.setCreateTime(DateUtils.getNowDate());
        return doctorsMapper.insertDoctors(doctors);
    }

    /**
     * 修改医生信息
     * 
     * @param doctors 医生信息
     * @return 结果
     */
    @Override
    public int updateDoctors(Doctors doctors)
    {
        doctors.setUpdateTime(DateUtils.getNowDate());
        return doctorsMapper.updateDoctors(doctors);
    }

    /**
     * 批量删除医生信息
     * 
     * @param doctorIds 需要删除的医生信息主键
     * @return 结果
     */
    @Override
    public int deleteDoctorsByDoctorIds(Long[] doctorIds)
    {
        return doctorsMapper.deleteDoctorsByDoctorIds(doctorIds);
    }

    /**
     * 删除医生信息信息
     * 
     * @param doctorId 医生信息主键
     * @return 结果
     */
    @Override
    public int deleteDoctorsByDoctorId(Long doctorId)
    {
        return doctorsMapper.deleteDoctorsByDoctorId(doctorId);
    }
}
