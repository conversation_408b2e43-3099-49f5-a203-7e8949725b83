package com.ruoyi.hospital.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.hospital.domain.MedicalRecords;
import com.ruoyi.hospital.mapper.MedicalRecordsMapper;
import com.ruoyi.hospital.service.IMedicalRecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 就诊记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class MedicalRecordsServiceImpl implements IMedicalRecordsService
{
    @Resource
    private MedicalRecordsMapper medicalRecordsMapper;

    /**
     * 查询就诊记录
     * 
     * @param recordId 就诊记录主键
     * @return 就诊记录
     */
    @Override
    public MedicalRecords selectMedicalRecordsByRecordId(Long recordId)
    {
        return medicalRecordsMapper.selectMedicalRecordsByRecordId(recordId);
    }

    /**
     * 查询就诊记录列表
     * 
     * @param medicalRecords 就诊记录
     * @return 就诊记录
     */
    @Override
    public List<MedicalRecords> selectMedicalRecordsList(MedicalRecords medicalRecords)
    {
        return medicalRecordsMapper.selectMedicalRecordsList(medicalRecords);
    }

    /**
     * 新增就诊记录
     * 
     * @param medicalRecords 就诊记录
     * @return 结果
     */
    @Override
    public int insertMedicalRecords(MedicalRecords medicalRecords)
    {
        medicalRecords.setCreateTime(DateUtils.getNowDate());
        return medicalRecordsMapper.insertMedicalRecords(medicalRecords);
    }

    /**
     * 修改就诊记录
     * 
     * @param medicalRecords 就诊记录
     * @return 结果
     */
    @Override
    public int updateMedicalRecords(MedicalRecords medicalRecords)
    {
        return medicalRecordsMapper.updateMedicalRecords(medicalRecords);
    }

    /**
     * 批量删除就诊记录
     * 
     * @param recordIds 需要删除的就诊记录主键
     * @return 结果
     */
    @Override
    public int deleteMedicalRecordsByRecordIds(Long[] recordIds)
    {
        return medicalRecordsMapper.deleteMedicalRecordsByRecordIds(recordIds);
    }

    /**
     * 删除就诊记录信息
     * 
     * @param recordId 就诊记录主键
     * @return 结果
     */
    @Override
    public int deleteMedicalRecordsByRecordId(Long recordId)
    {
        return medicalRecordsMapper.deleteMedicalRecordsByRecordId(recordId);
    }
}
