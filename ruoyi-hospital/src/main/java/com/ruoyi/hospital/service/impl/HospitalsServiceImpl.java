package com.ruoyi.hospital.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.hospital.domain.Hospitals;
import com.ruoyi.hospital.mapper.HospitalsMapper;
import com.ruoyi.hospital.service.IHospitalsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 医院信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class HospitalsServiceImpl implements IHospitalsService
{
    @Resource
    private HospitalsMapper hospitalsMapper;

    /**
     * 查询医院信息
     *
     * @param hospitalId 医院信息主键
     * @return 医院信息
     */
    @Override
    public Hospitals selectHospitalsByHospitalId(Long hospitalId)
    {
        return hospitalsMapper.selectHospitalsByHospitalId(hospitalId);
    }

    /**
     * 查询医院信息列表
     *
     * @param hospitals 医院信息
     * @return 医院信息
     */
    @Override
    public List<Hospitals> selectHospitalsList(Hospitals hospitals)
    {
        return hospitalsMapper.selectHospitalsList(hospitals);
    }

    /**
     * 新增医院信息
     *
     * @param hospitals 医院信息
     * @return 结果
     */
    @Override
    public int insertHospitals(Hospitals hospitals)
    {
        hospitals.setCreateTime(DateUtils.getNowDate());
        return hospitalsMapper.insertHospitals(hospitals);
    }

    /**
     * 修改医院信息
     *
     * @param hospitals 医院信息
     * @return 结果
     */
    @Override
    public int updateHospitals(Hospitals hospitals)
    {
        hospitals.setUpdateTime(DateUtils.getNowDate());
        return hospitalsMapper.updateHospitals(hospitals);
    }

    /**
     * 批量删除医院信息
     *
     * @param hospitalIds 需要删除的医院信息主键
     * @return 结果
     */
    @Override
    public int deleteHospitalsByHospitalIds(Long[] hospitalIds)
    {
        return hospitalsMapper.deleteHospitalsByHospitalIds(hospitalIds);
    }

    /**
     * 删除医院信息信息
     *
     * @param hospitalId 医院信息主键
     * @return 结果
     */
    @Override
    public int deleteHospitalsByHospitalId(Long hospitalId)
    {
        return hospitalsMapper.deleteHospitalsByHospitalId(hospitalId);
    }
}
