package com.ruoyi.hospital.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.hospital.domain.Schedules;
import com.ruoyi.hospital.mapper.SchedulesMapper;
import com.ruoyi.hospital.service.ISchedulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
;import javax.annotation.Resource;

/**
 * 医生排班Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class SchedulesServiceImpl implements ISchedulesService
{
    @Resource
    private SchedulesMapper schedulesMapper;

    /**
     * 查询医生排班
     * 
     * @param scheduleId 医生排班主键
     * @return 医生排班
     */
    @Override
    public Schedules selectSchedulesByScheduleId(Long scheduleId)
    {
        return schedulesMapper.selectSchedulesByScheduleId(scheduleId);
    }

    /**
     * 查询医生排班列表
     * 
     * @param schedules 医生排班
     * @return 医生排班
     */
    @Override
    public List<Schedules> selectSchedulesList(Schedules schedules)
    {
        return schedulesMapper.selectSchedulesList(schedules);
    }

    /**
     * 新增医生排班
     * 
     * @param schedules 医生排班
     * @return 结果
     */
    @Override
    public int insertSchedules(Schedules schedules)
    {
        schedules.setCreateTime(DateUtils.getNowDate());
        return schedulesMapper.insertSchedules(schedules);
    }

    /**
     * 修改医生排班
     * 
     * @param schedules 医生排班
     * @return 结果
     */
    @Override
    public int updateSchedules(Schedules schedules)
    {
        schedules.setUpdateTime(DateUtils.getNowDate());
        return schedulesMapper.updateSchedules(schedules);
    }

    /**
     * 批量删除医生排班
     * 
     * @param scheduleIds 需要删除的医生排班主键
     * @return 结果
     */
    @Override
    public int deleteSchedulesByScheduleIds(Long[] scheduleIds)
    {
        return schedulesMapper.deleteSchedulesByScheduleIds(scheduleIds);
    }

    /**
     * 删除医生排班信息
     * 
     * @param scheduleId 医生排班主键
     * @return 结果
     */
    @Override
    public int deleteSchedulesByScheduleId(Long scheduleId)
    {
        return schedulesMapper.deleteSchedulesByScheduleId(scheduleId);
    }
}
