package com.ruoyi.hospital.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 预约订单对象 appointments
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class Appointments extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 预约表（UUID生成） */
    private String appointmentId;

    /** 患者名称 */
    @Excel(name = "患者名称")
    private String patientName;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    @Excel(name = "预约日期")
    private String date;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    /** 医生名称 */
    @Excel(name = "医生名称")
    private String doctorName;

    /** 医院名称 */
    @Excel(name = "医院名称")
    private String hospitalName;

    /** 科室名称 */
    @Excel(name = "科室名称")
    private String deptName;


    /** 就诊时段 */
    @Excel(name = "就诊时段")
    private String timeSlot;

    /** 取消原因 */
    @Excel(name = "取消原因")
    private String cancelReason;

    public void setAppointmentId(String appointmentId) 
    {
        this.appointmentId = appointmentId;
    }

    public String getAppointmentId() 
    {
        return appointmentId;
    }

    public void setPatientName(String patientName) 
    {
        this.patientName = patientName;
    }

    public String getPatientName() 
    {
        return patientName;
    }

    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }

    public void setDoctorName(String doctorName) 
    {
        this.doctorName = doctorName;
    }

    public String getDoctorName() 
    {
        return doctorName;
    }

    public void setHospitalName(String hospitalName) 
    {
        this.hospitalName = hospitalName;
    }

    public String getHospitalName() 
    {
        return hospitalName;
    }

    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }

    public void setTimeSlot(String timeSlot) 
    {
        this.timeSlot = timeSlot;
    }

    public String getTimeSlot() 
    {
        return timeSlot;
    }

    public void setCancelReason(String cancelReason) 
    {
        this.cancelReason = cancelReason;
    }

    public String getCancelReason() 
    {
        return cancelReason;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("appointmentId", getAppointmentId())
            .append("patientName", getPatientName())
            .append("idCard", getIdCard())
            .append("doctorName", getDoctorName())
            .append("hospitalName", getHospitalName())
            .append("deptName", getDeptName())
            .append("timeSlot", getTimeSlot())
            .append("cancelReason", getCancelReason())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
                .append("date", getDate())
            .toString();
    }
}
