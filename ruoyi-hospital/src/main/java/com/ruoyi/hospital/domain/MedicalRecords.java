package com.ruoyi.hospital.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 就诊记录对象 medical_records
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class MedicalRecords extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long recordId;

    /** 关联预约订单号 */
    @Excel(name = "关联预约订单号")
    private String appointmentId;

    /** 患者名称 */
    @Excel(name = "患者名称")
    private Long patientName;

    /** 医生ID */
    @Excel(name = "医生ID")
    private Long doctorId;

    /** 诊断结果 */
    @Excel(name = "诊断结果")
    private String diagnosis;

    /** 治疗方案 */
    @Excel(name = "治疗方案")
    private String treatmentPlan;

    /** 处方内容 */
    @Excel(name = "处方内容")
    private String prescription;

    /** 检查项目 */
    @Excel(name = "检查项目")
    private String examinationItems;

    /** 就诊时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "就诊时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date visitTime;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }

    public void setAppointmentId(String appointmentId) 
    {
        this.appointmentId = appointmentId;
    }

    public String getAppointmentId() 
    {
        return appointmentId;
    }

    public void setPatientName(Long patientName) 
    {
        this.patientName = patientName;
    }

    public Long getPatientName() 
    {
        return patientName;
    }

    public void setDoctorId(Long doctorId) 
    {
        this.doctorId = doctorId;
    }

    public Long getDoctorId() 
    {
        return doctorId;
    }

    public void setDiagnosis(String diagnosis) 
    {
        this.diagnosis = diagnosis;
    }

    public String getDiagnosis() 
    {
        return diagnosis;
    }

    public void setTreatmentPlan(String treatmentPlan) 
    {
        this.treatmentPlan = treatmentPlan;
    }

    public String getTreatmentPlan() 
    {
        return treatmentPlan;
    }

    public void setPrescription(String prescription) 
    {
        this.prescription = prescription;
    }

    public String getPrescription() 
    {
        return prescription;
    }

    public void setExaminationItems(String examinationItems) 
    {
        this.examinationItems = examinationItems;
    }

    public String getExaminationItems() 
    {
        return examinationItems;
    }

    public void setVisitTime(Date visitTime) 
    {
        this.visitTime = visitTime;
    }

    public Date getVisitTime() 
    {
        return visitTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("appointmentId", getAppointmentId())
            .append("patientName", getPatientName())
            .append("doctorId", getDoctorId())
            .append("diagnosis", getDiagnosis())
            .append("treatmentPlan", getTreatmentPlan())
            .append("prescription", getPrescription())
            .append("examinationItems", getExaminationItems())
            .append("visitTime", getVisitTime())
            .append("createTime", getCreateTime())
            .toString();
    }
}
