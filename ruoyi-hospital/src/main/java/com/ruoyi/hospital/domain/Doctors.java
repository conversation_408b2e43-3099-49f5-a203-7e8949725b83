package com.ruoyi.hospital.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 医生信息对象 doctors
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class Doctors extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long doctorId;

    /** 所属科室ID */
    @Excel(name = "所属科室ID")
    private Long deptId;

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    @TableField(exist = false)
    @Excel(name = "所属科室名称")
    private String deptName;



    @Excel(name = "<UNK>ID")
    private Long patientId;

    @Excel(name = "医生姓名")
    private String doctorName;

    /** 职称 */
    @Excel(name = "职称")
    private String title;

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    /** 擅长领域 */
    @Excel(name = "擅长领域")
    private String specialty;

    /** 从业年限 */
    @Excel(name = "从业年限")
    private Long workYears;

    /** 挂号费（元） */
    @Excel(name = "挂号费", readConverterExp = "元=")
    private BigDecimal consultationFee;

    /** 医生简介 */
    @Excel(name = "医生简介")
    private String introduction;

    /** 删除标志（0=未删除，1=已删除） */
    @Excel(name = "删除标志", readConverterExp = "0==未删除，1=已删除")
    private Integer deleted;

    public void setDoctorId(Long doctorId) 
    {
        this.doctorId = doctorId;
    }

    public Long getDoctorId() 
    {
        return doctorId;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }

    public void setSpecialty(String specialty) 
    {
        this.specialty = specialty;
    }

    public String getSpecialty() 
    {
        return specialty;
    }

    public void setWorkYears(Long workYears) 
    {
        this.workYears = workYears;
    }

    public Long getWorkYears() 
    {
        return workYears;
    }

    public void setConsultationFee(BigDecimal consultationFee) 
    {
        this.consultationFee = consultationFee;
    }

    public BigDecimal getConsultationFee() 
    {
        return consultationFee;
    }

    public void setIntroduction(String introduction) 
    {
        this.introduction = introduction;
    }

    public String getIntroduction() 
    {
        return introduction;
    }

    public void setDeleted(Integer deleted) 
    {
        this.deleted = deleted;
    }

    public Integer getDeleted() 
    {
        return deleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("doctorId", getDoctorId())
            .append("deptId", getDeptId())
            .append("title", getTitle())
            .append("specialty", getSpecialty())
            .append("workYears", getWorkYears())
            .append("consultationFee", getConsultationFee())
            .append("introduction", getIntroduction())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("deleted", getDeleted())
            .toString();
    }
}
