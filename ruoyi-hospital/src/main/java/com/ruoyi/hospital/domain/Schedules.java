package com.ruoyi.hospital.domain;

import java.time.LocalTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 排班信息对象 schedules
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public class Schedules extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long scheduleId;

    /** 医生ID */
    @Excel(name = "医生ID")
    private Long doctorId;

    @TableField(exist = false)
    @Excel(name = "医生姓名")
    private String doctorName;

    @TableField(exist = false)
    @Excel(name = "科室名称")
    private String deptName;


    /** 排班日期 */
    @Excel(name = "排班日期")
    private String date;

    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "上午开始时间", width = 30, dateFormat = "HH:mm") // 修改Excel导出格式
    private LocalTime morningStart; // 类型改为 LocalTime

    /** 上午结束时间 */
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "上午结束时间", width = 30, dateFormat = "HH:mm")
    private LocalTime morningEnd;

    /** 下午开始时间 */
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "下午开始时间", width = 30, dateFormat = "HH:mm")
    private LocalTime afternoonStart;

    /** 下午结束时间 */
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "下午结束时间", width = 30, dateFormat = "HH:mm")
    private LocalTime afternoonEnd;

    /** 夜间开始时间 */
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "夜间开始时间", width = 30, dateFormat = "HH:mm")
    private LocalTime nightStart;

    /** 夜间结束时间 */
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "夜间结束时间", width = 30, dateFormat = "HH:mm")
    private LocalTime nightEnd;

    /** 上午号源数量 */
    @Excel(name = "上午号源数量")
    private Long morningQuota;

    /** 下午号源数量 */
    @Excel(name = "下午号源数量")
    private Long afternoonQuota;

    /** 夜间号源数量 */
    @Excel(name = "夜间号源数量")
    private Long nightQuota;

    /** 上午剩余号源 */
    @Excel(name = "上午剩余号源")
    private Long morningRemaining;

    /** 下午剩余号源 */
    @Excel(name = "下午剩余号源")
    private Long afternoonRemaining;

    /** 夜间剩余号源 */
    @Excel(name = "夜间剩余号源")
    private Long nightRemaining;

    /** 删除标志（0=未删除，1=已删除） */
    private Integer deleted;


    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public LocalTime getAfternoonEnd() {
        return afternoonEnd;
    }

    public void setAfternoonEnd(LocalTime afternoonEnd) {
        this.afternoonEnd = afternoonEnd;
    }

    public Long getAfternoonQuota() {
        return afternoonQuota;
    }

    public void setAfternoonQuota(Long afternoonQuota) {
        this.afternoonQuota = afternoonQuota;
    }

    public Long getAfternoonRemaining() {
        return afternoonRemaining;
    }

    public void setAfternoonRemaining(Long afternoonRemaining) {
        this.afternoonRemaining = afternoonRemaining;
    }

    public LocalTime getAfternoonStart() {
        return afternoonStart;
    }

    public void setAfternoonStart(LocalTime afternoonStart) {
        this.afternoonStart = afternoonStart;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Long getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Long doctorId) {
        this.doctorId = doctorId;
    }

    public LocalTime getMorningEnd() {
        return morningEnd;
    }

    public void setMorningEnd(LocalTime morningEnd) {
        this.morningEnd = morningEnd;
    }

    public Long getMorningQuota() {
        return morningQuota;
    }

    public void setMorningQuota(Long morningQuota) {
        this.morningQuota = morningQuota;
    }

    public Long getMorningRemaining() {
        return morningRemaining;
    }

    public void setMorningRemaining(Long morningRemaining) {
        this.morningRemaining = morningRemaining;
    }

    public LocalTime getMorningStart() {
        return morningStart;
    }

    public void setMorningStart(LocalTime morningStart) {
        this.morningStart = morningStart;
    }

    public LocalTime getNightEnd() {
        return nightEnd;
    }

    public void setNightEnd(LocalTime nightEnd) {
        this.nightEnd = nightEnd;
    }

    public Long getNightQuota() {
        return nightQuota;
    }

    public void setNightQuota(Long nightQuota) {
        this.nightQuota = nightQuota;
    }

    public Long getNightRemaining() {
        return nightRemaining;
    }

    public void setNightRemaining(Long nightRemaining) {
        this.nightRemaining = nightRemaining;
    }

    public LocalTime getNightStart() {
        return nightStart;
    }

    public void setNightStart(LocalTime nightStart) {
        this.nightStart = nightStart;
    }

    public Long getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Long scheduleId) {
        this.scheduleId = scheduleId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("scheduleId", getScheduleId())
                .append("doctorId", getDoctorId())
                .append("date", getDate())
                .append("morningStart", getMorningStart())
                .append("morningEnd", getMorningEnd())
                .append("afternoonStart", getAfternoonStart())
                .append("afternoonEnd", getAfternoonEnd())
                .append("nightStart", getNightStart())
                .append("nightEnd", getNightEnd())
                .append("morningQuota", getMorningQuota())
                .append("afternoonQuota", getAfternoonQuota())
                .append("nightQuota", getNightQuota())
                .append("morningRemaining", getMorningRemaining())
                .append("afternoonRemaining", getAfternoonRemaining())
                .append("nightRemaining", getNightRemaining())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("deleted", getDeleted())

                .toString();
    }
}
