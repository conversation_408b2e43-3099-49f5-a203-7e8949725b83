/*
SQLyog Ultimate v11.33 (64 bit)
MySQL - 8.0.31 : Database - ry-vue
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`ry-vue` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `ry-vue`;

/*Table structure for table `appointments` */

DROP TABLE IF EXISTS `appointments`;

CREATE TABLE `appointments` (
  `appointment_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '预约表（UUID生成）',
  `patient_name` varchar(20) DEFAULT NULL COMMENT '患者名称',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `doctor_name` varchar(20) DEFAULT NULL COMMENT '医生名称',
  `hospital_name` varchar(20) DEFAULT NULL COMMENT '医院名称',
  `dept_name` varchar(20) DEFAULT NULL COMMENT '科室名称',
  `date` varchar(20) DEFAULT NULL COMMENT '就诊日期',
  `time_slot` varchar(20) NOT NULL COMMENT '就诊时段',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '预约时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`appointment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='预约订单表';

/*Data for the table `appointments` */

insert  into `appointments`(`appointment_id`,`patient_name`,`id_card`,`doctor_name`,`hospital_name`,`dept_name`,`date`,`time_slot`,`cancel_reason`,`create_time`,`update_time`) values ('01ef454d-6204-4498-9ace-c132b2bc2040','天才少年','130123201002041254','赵六','协和医院','疑难病科','2025-07-08','上午',NULL,'2025-07-05 16:37:49','2025-07-07 20:10:36'),('119d2b74-f0a2-4273-bf1e-ce55e38a27cf','量山','130156205121325411','陈芳','协和医院','眼科','2025-07-03','夜间',NULL,'2025-07-05 18:12:22','2025-07-07 20:52:42'),('1d3a25f0-5079-440f-ace9-e8b835308f62','吴婷','******************','陈芳','协和医院','眼科','2025-07-03','20:00-23:00',NULL,'2025-07-08 15:33:59','2025-07-08 15:33:59'),('50452d72-56ab-4885-8327-228481b1c4ea','陈静','330106198704056789','王五',NULL,'眼科','2025-07-21','下午',NULL,'2025-07-08 15:17:07','2025-07-08 15:17:07'),('85199e8e-7475-4ef6-af26-065ea05a4993','周浩','741852963789','赵六',NULL,'疑难病科','2025-07-09','07:03 - 11:03',NULL,'2025-07-08 14:40:55','2025-07-08 14:40:55'),('f801e32d-b86f-47e1-bd56-7c0a63dc0ca3','赵雨艳','1346549658564','陈芳',NULL,'眼科',NULL,'07:04 - 10:08',NULL,'2025-07-08 14:33:21','2025-07-08 14:33:21'),('ff27ac69-c025-48d8-aa27-50c2727157df','大帅','5464686568965','陈芳','协和医院','眼科','2025-07-03','下午',NULL,'2025-07-08 16:22:08','2025-07-08 16:22:08');

/*Table structure for table `departments` */

DROP TABLE IF EXISTS `departments`;

CREATE TABLE `departments` (
  `dept_id` int NOT NULL AUTO_INCREMENT,
  `dept_name` varchar(50) NOT NULL COMMENT '科室名称',
  `hospital_id` int NOT NULL COMMENT '所属医院ID',
  `description` text COMMENT '科室简介',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0=未删除，1=已删除）',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='科室信息表';

/*Data for the table `departments` */

insert  into `departments`(`dept_id`,`dept_name`,`hospital_id`,`description`,`create_time`,`update_time`,`deleted`) values (1,'眼科',1,'专业诊治各类眼科疾病','2025-07-03 14:25:07','2025-07-07 17:22:53',0),(2,'疑难病科',1,'专攻各类疑难病症诊断与治疗','2025-07-04 17:34:47','2025-07-07 17:24:01',0),(3,'神经内科	',2,'神经系统疾病专业诊疗中心','2025-07-07 15:51:10','2025-07-07 17:23:53',0),(4,'肾脏内科',3,'肾脏疾病诊疗与透析治疗','2025-07-07 16:09:31','2025-07-07 17:23:45',0),(5,'呼吸内科',5,'呼吸道疾病预防与治疗，倡导健康生活','2025-07-07 16:21:35','2025-07-07 17:23:37',0),(6,'儿科',3,'专注于儿童疾病预防与治疗，提供温馨就诊环境，医护团队经验丰富。','2025-07-07 20:12:32','2025-07-07 20:12:32',0),(7,'妇产科',6,'提供孕产全程管理服务，设有VIP产房，技术力量雄厚。','2025-07-07 20:13:50','2025-07-07 20:13:49',0);

/*Table structure for table `doctors` */

DROP TABLE IF EXISTS `doctors`;

CREATE TABLE `doctors` (
  `doctor_id` int NOT NULL AUTO_INCREMENT,
  `doctor_name` varchar(20) DEFAULT NULL COMMENT '医生姓名',
  `dept_id` int NOT NULL COMMENT '所属科室ID',
  `title` enum('主任医师','副主任医师','主治医师','住院医师') NOT NULL COMMENT '职称',
  `specialty` varchar(100) DEFAULT NULL COMMENT '擅长领域',
  `work_years` int DEFAULT NULL COMMENT '从业年限',
  `consultation_fee` decimal(10,2) NOT NULL COMMENT '挂号费（元）',
  `introduction` text COMMENT '医生简介',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0=未删除，1=已删除）',
  PRIMARY KEY (`doctor_id`,`title`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='医生信息表';

/*Data for the table `doctors` */

insert  into `doctors`(`doctor_id`,`doctor_name`,`dept_id`,`title`,`specialty`,`work_years`,`consultation_fee`,`introduction`,`create_time`,`update_time`,`deleted`) values (1,'陈芳',1,'主任医师','白内障超声乳化、青光眼',20,'60.00','中山眼科中心博士生导师，国内首批开展飞秒激光手术的专家之一。','2025-07-03 14:26:29','2025-07-07 20:23:39',0),(2,'刘婷',7,'副主任医师','高危妊娠、妇科肿瘤',12,'40.00','复旦大学附属妇产科医院产科组长，擅长多胎妊娠管理，接生超3000例。','2025-07-04 16:22:52','2025-07-07 20:24:15',0),(3,'王五',1,'主任医师','冠心病介入治疗、高血压管理',5,'150.00','北京医科大学博士，从事心血管疾病诊疗20年，完成冠脉介入手术3000余例。','2025-07-04 16:35:22','2025-07-08 15:39:35',0),(4,'赵六',2,'住院医师','脑肿瘤显微手术、脑血管病',20,'500.00','美国约翰霍普金斯医院访问学者，完成高难度脑外科手术1500余例，手术精准度高。','2025-07-04 17:37:16','2025-07-08 15:40:11',0),(5,'李雯',6,'副主任医师','儿童哮喘、新生儿疾病',10,'30.00','上海儿童医学中心骨干医生，曾赴美国波士顿儿童医院进修，诊疗耐心细致。','2025-07-04 17:39:34','2025-07-07 20:18:39',0),(6,'王建国',2,'主任医师','高血压、冠心病、心力衰竭',15,'50.00','北京协和医院心血管内科专家，擅长复杂冠心病介入治疗，发表SCI论文20余篇。','2025-07-04 17:45:07','2025-07-07 20:22:42',0);

/*Table structure for table `gen_table` */

DROP TABLE IF EXISTS `gen_table`;

CREATE TABLE `gen_table` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='代码生成业务表';

/*Data for the table `gen_table` */

insert  into `gen_table`(`table_id`,`table_name`,`table_comment`,`sub_table_name`,`sub_table_fk_name`,`class_name`,`tpl_category`,`tpl_web_type`,`package_name`,`module_name`,`business_name`,`function_name`,`function_author`,`gen_type`,`gen_path`,`options`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,'hospitals','医院信息表',NULL,NULL,'Hospitals','crud','','com.ruoyi.system','system','hospitals','医院信息','ruoyi','0','/',NULL,'admin','2025-07-01 14:29:23','',NULL,NULL),(2,'appointments','预约订单表',NULL,NULL,'Appointments','crud','','com.ruoyi.system','system','appointments','预约订单','ruoyi','0','/',NULL,'admin','2025-07-01 14:36:21','',NULL,NULL),(3,'departments','科室信息表',NULL,NULL,'Departments','crud','element-ui','com.ruoyi.system','system','departments','科室信息','ruoyi','0','/','{}','admin','2025-07-01 14:36:21','','2025-07-07 16:06:13',NULL),(4,'doctors','医生信息表',NULL,NULL,'Doctors','crud','','com.ruoyi.system','system','doctors','医生信息','ruoyi','0','/',NULL,'admin','2025-07-01 14:36:21','',NULL,NULL),(5,'medical_records','就诊记录表',NULL,NULL,'MedicalRecords','crud','','com.ruoyi.system','system','records','就诊记录','ruoyi','0','/',NULL,'admin','2025-07-01 14:36:21','',NULL,NULL),(6,'schedules','医生排班表',NULL,NULL,'Schedules','crud','','com.ruoyi.system','system','schedules','医生排班','ruoyi','0','/',NULL,'admin','2025-07-01 14:36:21','',NULL,NULL);

/*Table structure for table `gen_table_column` */

DROP TABLE IF EXISTS `gen_table_column`;

CREATE TABLE `gen_table_column` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) DEFAULT '' COMMENT '字典类型',
  `sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`)
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='代码生成业务表字段';

/*Data for the table `gen_table_column` */

insert  into `gen_table_column`(`column_id`,`table_id`,`column_name`,`column_comment`,`column_type`,`java_type`,`java_field`,`is_pk`,`is_increment`,`is_required`,`is_insert`,`is_edit`,`is_list`,`is_query`,`query_type`,`html_type`,`dict_type`,`sort`,`create_by`,`create_time`,`update_by`,`update_time`) values (1,1,'hospital_id',NULL,'int','Long','hospitalId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-07-01 14:29:23','',NULL),(2,1,'hospital_name','医院名称','varchar(100)','String','hospitalName','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-07-01 14:29:23','',NULL),(3,1,'hospital_level','医院等级','enum(\'三级甲等\',\'三级乙等\',\'二级甲等\',\'一级\',\'其他\')','String','hospitalLevel','0','0','1','1','1','1','1','EQ',NULL,'',3,'admin','2025-07-01 14:29:23','',NULL),(4,1,'address','地址','varchar(200)','String','address','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-07-01 14:29:23','',NULL),(5,1,'phone','联系电话','varchar(20)','String','phone','0','0','0','1','1','1','1','EQ','input','',5,'admin','2025-07-01 14:29:23','',NULL),(6,1,'description','医院简介','text','String','description','0','0','0','1','1','1','1','EQ','textarea','',6,'admin','2025-07-01 14:29:23','',NULL),(7,1,'create_time','创建时间','datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',7,'admin','2025-07-01 14:29:23','',NULL),(8,1,'update_time',NULL,'datetime','Date','updateTime','0','0','0','1','1',NULL,NULL,'EQ','datetime','',8,'admin','2025-07-01 14:29:23','',NULL),(9,1,'deleted','删除标志（0=未删除，1=已删除）','tinyint(1)','Integer','deleted','0','0','0','1','1','1','1','EQ','input','',9,'admin','2025-07-01 14:29:23','',NULL),(10,2,'appointment_id','预约表（UUID生成）','varchar(32)','String','appointmentId','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-07-01 14:36:21','',NULL),(11,2,'patient_name','患者名称','varchar(20)','String','patientName','0','0','0','1','1','1','1','LIKE','input','',2,'admin','2025-07-01 14:36:21','',NULL),(12,2,'id_card','身份证号','varchar(20)','String','idCard','0','0','0','1','1','1','1','EQ','input','',3,'admin','2025-07-01 14:36:21','',NULL),(13,2,'doctor_name','医生名称','varchar(20)','String','doctorName','0','0','0','1','1','1','1','LIKE','input','',4,'admin','2025-07-01 14:36:21','',NULL),(14,2,'hospital_name','医院名称','varchar(20)','String','hospitalName','0','0','0','1','1','1','1','LIKE','input','',5,'admin','2025-07-01 14:36:21','',NULL),(15,2,'dept_name','科室名称','varchar(20)','String','deptName','0','0','0','1','1','1','1','LIKE','input','',6,'admin','2025-07-01 14:36:21','',NULL),(16,2,'time_slot','就诊时段','enum(\'上午\',\'下午\',\'夜间\')','String','timeSlot','0','0','1','1','1','1','1','EQ',NULL,'',7,'admin','2025-07-01 14:36:21','',NULL),(17,2,'cancel_reason','取消原因','varchar(200)','String','cancelReason','0','0','0','1','1','1','1','EQ','input','',8,'admin','2025-07-01 14:36:21','',NULL),(18,2,'create_time','预约时间','datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',9,'admin','2025-07-01 14:36:21','',NULL),(19,2,'update_time','更新时间','datetime','Date','updateTime','0','0','0','1','1',NULL,NULL,'EQ','datetime','',10,'admin','2025-07-01 14:36:21','',NULL),(20,3,'dept_id',NULL,'int','Long','deptId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-07-01 14:36:21','','2025-07-07 16:06:13'),(21,3,'dept_name','科室名称','varchar(50)','String','deptName','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-07-01 14:36:21','','2025-07-07 16:06:13'),(22,3,'hospital_id','所属医院ID','int','Long','hospitalId','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-07-01 14:36:21','','2025-07-07 16:06:13'),(23,3,'description','科室简介','text','String','description','0','0','0','1','1','1','1','EQ','editor','',4,'admin','2025-07-01 14:36:21','','2025-07-07 16:06:13'),(24,3,'create_time','创建时间','datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',5,'admin','2025-07-01 14:36:21','','2025-07-07 16:06:13'),(25,3,'update_time',NULL,'datetime','Date','updateTime','0','0','0','1','1',NULL,NULL,'EQ','datetime','',6,'admin','2025-07-01 14:36:21','','2025-07-07 16:06:13'),(26,3,'deleted','删除标志（0=未删除，1=已删除）','tinyint(1)','Integer','deleted','0','0','0','1','1','1','1','EQ','input','',7,'admin','2025-07-01 14:36:21','','2025-07-07 16:06:13'),(27,4,'doctor_id',NULL,'int','Long','doctorId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-07-01 14:36:21','',NULL),(28,4,'dept_id','所属科室ID','int','Long','deptId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-07-01 14:36:21','',NULL),(29,4,'title','职称','enum(\'主任医师\',\'副主任医师\',\'主治医师\',\'住院医师\')','String','title','0','0','1','1','1','1','1','EQ',NULL,'',3,'admin','2025-07-01 14:36:21','',NULL),(30,4,'specialty','擅长领域','varchar(100)','String','specialty','0','0','0','1','1','1','1','EQ','input','',4,'admin','2025-07-01 14:36:21','',NULL),(31,4,'work_years','从业年限','int','Long','workYears','0','0','0','1','1','1','1','EQ','input','',5,'admin','2025-07-01 14:36:21','',NULL),(32,4,'consultation_fee','挂号费（元）','decimal(10,2)','BigDecimal','consultationFee','0','0','1','1','1','1','1','EQ','input','',6,'admin','2025-07-01 14:36:21','',NULL),(33,4,'introduction','医生简介','text','String','introduction','0','0','0','1','1','1','1','EQ','textarea','',7,'admin','2025-07-01 14:36:21','',NULL),(34,4,'create_time','创建时间','datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',8,'admin','2025-07-01 14:36:21','',NULL),(35,4,'update_time',NULL,'datetime','Date','updateTime','0','0','0','1','1',NULL,NULL,'EQ','datetime','',9,'admin','2025-07-01 14:36:21','',NULL),(36,4,'deleted','删除标志（0=未删除，1=已删除）','tinyint(1)','Integer','deleted','0','0','0','1','1','1','1','EQ','input','',10,'admin','2025-07-01 14:36:21','',NULL),(37,5,'record_id',NULL,'int','Long','recordId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-07-01 14:36:21','',NULL),(38,5,'appointment_id','关联预约订单号','varchar(32)','String','appointmentId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-07-01 14:36:21','',NULL),(39,5,'patient_name','患者名称','int','Long','patientName','0','0','1','1','1','1','1','LIKE','input','',3,'admin','2025-07-01 14:36:21','',NULL),(40,5,'doctor_id','医生ID','int','Long','doctorId','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-07-01 14:36:21','',NULL),(41,5,'diagnosis','诊断结果','text','String','diagnosis','0','0','0','1','1','1','1','EQ','textarea','',5,'admin','2025-07-01 14:36:21','',NULL),(42,5,'treatment_plan','治疗方案','text','String','treatmentPlan','0','0','0','1','1','1','1','EQ','textarea','',6,'admin','2025-07-01 14:36:21','',NULL),(43,5,'prescription','处方内容','text','String','prescription','0','0','0','1','1','1','1','EQ','textarea','',7,'admin','2025-07-01 14:36:21','',NULL),(44,5,'examination_items','检查项目','text','String','examinationItems','0','0','0','1','1','1','1','EQ','textarea','',8,'admin','2025-07-01 14:36:21','',NULL),(45,5,'visit_time','就诊时间','datetime','Date','visitTime','0','0','0','1','1','1','1','EQ','datetime','',9,'admin','2025-07-01 14:36:21','',NULL),(46,5,'create_time','记录时间','datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',10,'admin','2025-07-01 14:36:21','',NULL),(47,6,'schedule_id',NULL,'int','Long','scheduleId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-07-01 14:36:21','',NULL),(48,6,'doctor_id','医生ID','int','Long','doctorId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-07-01 14:36:21','',NULL),(49,6,'date','排班日期','varchar(20)','String','date','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-07-01 14:36:21','',NULL),(50,6,'morning_start','上午开始时间','time','Date','morningStart','0','0','0','1','1','1','1','EQ','datetime','',4,'admin','2025-07-01 14:36:21','',NULL),(51,6,'morning_end','上午结束时间','time','Date','morningEnd','0','0','0','1','1','1','1','EQ','datetime','',5,'admin','2025-07-01 14:36:21','',NULL),(52,6,'afternoon_start','下午开始时间','time','Date','afternoonStart','0','0','0','1','1','1','1','EQ','datetime','',6,'admin','2025-07-01 14:36:21','',NULL),(53,6,'afternoon_end','下午结束时间','time','Date','afternoonEnd','0','0','0','1','1','1','1','EQ','datetime','',7,'admin','2025-07-01 14:36:21','',NULL),(54,6,'night_start','夜间开始时间','time','Date','nightStart','0','0','0','1','1','1','1','EQ','datetime','',8,'admin','2025-07-01 14:36:21','',NULL),(55,6,'night_end','夜间结束时间','time','Date','nightEnd','0','0','0','1','1','1','1','EQ','datetime','',9,'admin','2025-07-01 14:36:21','',NULL),(56,6,'morning_quota','上午号源数量','int','Long','morningQuota','0','0','0','1','1','1','1','EQ','input','',10,'admin','2025-07-01 14:36:21','',NULL),(57,6,'afternoon_quota','下午号源数量','int','Long','afternoonQuota','0','0','0','1','1','1','1','EQ','input','',11,'admin','2025-07-01 14:36:21','',NULL),(58,6,'night_quota','夜间号源数量','int','Long','nightQuota','0','0','0','1','1','1','1','EQ','input','',12,'admin','2025-07-01 14:36:21','',NULL),(59,6,'morning_remaining','上午剩余号源','int','Long','morningRemaining','0','0','0','1','1','1','1','EQ','input','',13,'admin','2025-07-01 14:36:21','',NULL),(60,6,'afternoon_remaining','下午剩余号源','int','Long','afternoonRemaining','0','0','0','1','1','1','1','EQ','input','',14,'admin','2025-07-01 14:36:21','',NULL),(61,6,'night_remaining','夜间剩余号源','int','Long','nightRemaining','0','0','0','1','1','1','1','EQ','input','',15,'admin','2025-07-01 14:36:21','',NULL),(62,6,'create_time','创建时间','datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',16,'admin','2025-07-01 14:36:21','',NULL),(63,6,'update_time',NULL,'datetime','Date','updateTime','0','0','0','1','1',NULL,NULL,'EQ','datetime','',17,'admin','2025-07-01 14:36:21','',NULL),(64,6,'deleted','删除标志（0=未删除，1=已删除）','tinyint(1)','Integer','deleted','0','0','0','1','1','1','1','EQ','input','',18,'admin','2025-07-01 14:36:21','',NULL);

/*Table structure for table `hospitals` */

DROP TABLE IF EXISTS `hospitals`;

CREATE TABLE `hospitals` (
  `hospital_id` int NOT NULL AUTO_INCREMENT,
  `hospital_name` varchar(100) NOT NULL COMMENT '医院名称',
  `hospital_level` enum('三级甲等','三级乙等','二级甲等','一级','其他') NOT NULL COMMENT '医院等级',
  `address` varchar(200) NOT NULL COMMENT '地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `description` text COMMENT '医院简介',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0=未删除，1=已删除）',
  PRIMARY KEY (`hospital_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='医院信息表';

/*Data for the table `hospitals` */

insert  into `hospitals`(`hospital_id`,`hospital_name`,`hospital_level`,`address`,`phone`,`description`,`create_time`,`update_time`,`deleted`) values (1,'协和医院','三级甲等','北京市','15533681891','北京协和医院是一所集医疗、教学、科研于一体的大型综合医院，是国家卫生健康委员会指定的全国疑难重症诊治指导中心。','2025-07-01 20:20:23','2025-07-08 09:27:06',0),(2,'唐都医院','三级甲等','陕西省西安市西安工程大学临潼校区','15353564523','唐都医院是空军军医大学（原第四军医大学）第二附属医院，坐落于古城西安，是一所集医疗、教学、科研、预防、保健和康复为一体的现代化三级甲等医院。医院占地面积480亩，展开床位2000余张，年门急诊量200余万人次。','2025-07-07 14:52:34','2025-07-08 15:41:00',0),(3,'上海瑞金医院','三级甲等','上海市黄浦区瑞金二路197号','021-64370045','上海瑞金医院是上海交通大学医学院附属医院，以血液病、内分泌代谢病、微创手术等为特色专科。','2025-07-07 15:12:20','2025-07-07 15:12:20',0),(4,'广州中山医院','三级乙等','广东省广州市越秀区中山二路58号','020-81332299','广州中山医院是华南地区著名的综合性医院，以心血管病、肿瘤治疗和器官移植见长。','2025-07-07 15:12:46','2025-07-07 15:12:46',0),(5,'深圳人民医院','二级甲等','广东省深圳市罗湖区东门北路1017号','0755-25533018','深圳人民医院是深圳市最早的三甲医院之一，以创伤急救、肿瘤综合治疗为特色。','2025-07-07 15:13:16','2025-07-07 15:13:16',0),(6,'复旦大学附属妇产科医院','三级甲等','上海市','0212-21558','作为中国历史最悠久的妇产科专科医院，医院由美国玛格丽特·威廉逊女士捐资创建，是我国最早成立的妇产科专科医院之一。医院现有杨浦院区和黄浦院区，总建筑面积10万平方米，开放床位820张，年门诊量150万人次，年分娩量约2万例。','2025-07-07 20:13:32','2025-07-08 15:41:35',0);

/*Table structure for table `medical_records` */

DROP TABLE IF EXISTS `medical_records`;

CREATE TABLE `medical_records` (
  `record_id` int NOT NULL AUTO_INCREMENT,
  `appointment_id` varchar(32) NOT NULL COMMENT '关联预约订单号',
  `patient_name` int NOT NULL COMMENT '患者名称',
  `doctor_id` int NOT NULL COMMENT '医生ID',
  `diagnosis` text COMMENT '诊断结果',
  `treatment_plan` text COMMENT '治疗方案',
  `prescription` text COMMENT '处方内容',
  `examination_items` text COMMENT '检查项目',
  `visit_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '就诊时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  PRIMARY KEY (`record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='就诊记录表';

/*Data for the table `medical_records` */

/*Table structure for table `qrtz_blob_triggers` */

DROP TABLE IF EXISTS `qrtz_blob_triggers`;

CREATE TABLE `qrtz_blob_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Blob类型的触发器表';

/*Data for the table `qrtz_blob_triggers` */

/*Table structure for table `qrtz_calendars` */

DROP TABLE IF EXISTS `qrtz_calendars`;

CREATE TABLE `qrtz_calendars` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`,`calendar_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日历信息表';

/*Data for the table `qrtz_calendars` */

/*Table structure for table `qrtz_cron_triggers` */

DROP TABLE IF EXISTS `qrtz_cron_triggers`;

CREATE TABLE `qrtz_cron_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Cron类型的触发器表';

/*Data for the table `qrtz_cron_triggers` */

/*Table structure for table `qrtz_fired_triggers` */

DROP TABLE IF EXISTS `qrtz_fired_triggers`;

CREATE TABLE `qrtz_fired_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) NOT NULL COMMENT '状态',
  `job_name` varchar(200) DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`,`entry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='已触发的触发器表';

/*Data for the table `qrtz_fired_triggers` */

/*Table structure for table `qrtz_job_details` */

DROP TABLE IF EXISTS `qrtz_job_details`;

CREATE TABLE `qrtz_job_details` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) NOT NULL COMMENT '任务组名',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`job_name`,`job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务详细信息表';

/*Data for the table `qrtz_job_details` */

/*Table structure for table `qrtz_locks` */

DROP TABLE IF EXISTS `qrtz_locks`;

CREATE TABLE `qrtz_locks` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`,`lock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='存储的悲观锁信息表';

/*Data for the table `qrtz_locks` */

/*Table structure for table `qrtz_paused_trigger_grps` */

DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;

CREATE TABLE `qrtz_paused_trigger_grps` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='暂停的触发器表';

/*Data for the table `qrtz_paused_trigger_grps` */

/*Table structure for table `qrtz_scheduler_state` */

DROP TABLE IF EXISTS `qrtz_scheduler_state`;

CREATE TABLE `qrtz_scheduler_state` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`,`instance_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='调度器状态表';

/*Data for the table `qrtz_scheduler_state` */

/*Table structure for table `qrtz_simple_triggers` */

DROP TABLE IF EXISTS `qrtz_simple_triggers`;

CREATE TABLE `qrtz_simple_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='简单触发器的信息表';

/*Data for the table `qrtz_simple_triggers` */

/*Table structure for table `qrtz_simprop_triggers` */

DROP TABLE IF EXISTS `qrtz_simprop_triggers`;

CREATE TABLE `qrtz_simprop_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='同步机制的行锁表';

/*Data for the table `qrtz_simprop_triggers` */

/*Table structure for table `qrtz_triggers` */

DROP TABLE IF EXISTS `qrtz_triggers`;

CREATE TABLE `qrtz_triggers` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  KEY `sched_name` (`sched_name`,`job_name`,`job_group`),
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='触发器详细信息表';

/*Data for the table `qrtz_triggers` */

/*Table structure for table `schedules` */

DROP TABLE IF EXISTS `schedules`;

CREATE TABLE `schedules` (
  `schedule_id` int NOT NULL AUTO_INCREMENT,
  `doctor_id` int NOT NULL COMMENT '医生ID',
  `date` varchar(20) NOT NULL COMMENT '排班日期',
  `morning_start` time DEFAULT NULL COMMENT '上午开始时间',
  `morning_end` time DEFAULT NULL COMMENT '上午结束时间',
  `afternoon_start` time DEFAULT NULL COMMENT '下午开始时间',
  `afternoon_end` time DEFAULT NULL COMMENT '下午结束时间',
  `night_start` time DEFAULT NULL COMMENT '夜间开始时间',
  `night_end` time DEFAULT NULL COMMENT '夜间结束时间',
  `morning_quota` int DEFAULT '0' COMMENT '上午号源数量',
  `afternoon_quota` int DEFAULT '0' COMMENT '下午号源数量',
  `night_quota` int DEFAULT '0' COMMENT '夜间号源数量',
  `morning_remaining` int DEFAULT '0' COMMENT '上午剩余号源',
  `afternoon_remaining` int DEFAULT '0' COMMENT '下午剩余号源',
  `night_remaining` int DEFAULT '0' COMMENT '夜间剩余号源',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0=未删除，1=已删除）',
  PRIMARY KEY (`schedule_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='医生排班表';

/*Data for the table `schedules` */

insert  into `schedules`(`schedule_id`,`doctor_id`,`date`,`morning_start`,`morning_end`,`afternoon_start`,`afternoon_end`,`night_start`,`night_end`,`morning_quota`,`afternoon_quota`,`night_quota`,`morning_remaining`,`afternoon_remaining`,`night_remaining`,`create_time`,`update_time`,`deleted`) values (1,1,'2025-07-03','07:04:00','10:08:00','15:09:00','17:09:00','20:00:00','23:00:00',2,3,5,2,0,2,'2025-07-03 14:35:40','2025-07-08 16:22:08',0),(2,2,'2025-07-03','03:15:00','05:00:00',NULL,NULL,NULL,NULL,12,12,12,0,0,0,'2025-07-03 14:35:58','2025-07-08 15:48:35',0),(3,3,'2025-07-21','21:12:00','21:12:00',NULL,NULL,NULL,NULL,12,12,12,2,10,10,'2025-07-04 21:12:16','2025-07-08 15:48:35',0),(4,4,'2025-07-09','07:03:00','11:03:00','14:02:00','17:03:00','20:03:00','23:03:00',12,12,12,2,2,2,'2025-07-07 20:03:49','2025-07-08 15:48:35',0);

/*Table structure for table `sys_config` */

DROP TABLE IF EXISTS `sys_config`;

CREATE TABLE `sys_config` (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='参数配置表';

/*Data for the table `sys_config` */

insert  into `sys_config`(`config_id`,`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,'主框架页-默认皮肤样式名称','sys.index.skinName','skin-blue','Y','admin','2025-06-24 11:49:29','',NULL,'蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),(2,'用户管理-账号初始密码','sys.user.initPassword','123456','Y','admin','2025-06-24 11:49:29','',NULL,'初始化密码 123456'),(3,'主框架页-侧边栏主题','sys.index.sideTheme','theme-dark','Y','admin','2025-06-24 11:49:29','',NULL,'深色主题theme-dark，浅色主题theme-light'),(4,'账号自助-验证码开关','sys.account.captchaEnabled','true','Y','admin','2025-06-24 11:49:29','',NULL,'是否开启验证码功能（true开启，false关闭）'),(5,'账号自助-是否开启用户注册功能','sys.account.registerUser','false','Y','admin','2025-06-24 11:49:29','',NULL,'是否开启注册用户功能（true开启，false关闭）'),(6,'用户登录-黑名单列表','sys.login.blackIPList','','Y','admin','2025-06-24 11:49:29','',NULL,'设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）'),(7,'用户管理-初始密码修改策略','sys.account.initPasswordModify','1','Y','admin','2025-06-24 11:49:29','',NULL,'0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框'),(8,'用户管理-账号密码更新周期','sys.account.passwordValidateDays','0','Y','admin','2025-06-24 11:49:29','',NULL,'密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');

/*Table structure for table `sys_dept` */

DROP TABLE IF EXISTS `sys_dept`;

CREATE TABLE `sys_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) DEFAULT '' COMMENT '部门名称',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `status` char(1) DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=200 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='部门表';

/*Data for the table `sys_dept` */

insert  into `sys_dept`(`dept_id`,`parent_id`,`ancestors`,`dept_name`,`order_num`,`leader`,`phone`,`email`,`status`,`del_flag`,`create_by`,`create_time`,`update_by`,`update_time`) values (100,0,'0','若依科技',0,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(101,100,'0,100','深圳总公司',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(102,100,'0,100','长沙分公司',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(103,101,'0,100,101','研发部门',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(104,101,'0,100,101','市场部门',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(105,101,'0,100,101','测试部门',3,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(106,101,'0,100,101','财务部门',4,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(107,101,'0,100,101','运维部门',5,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(108,102,'0,100,102','市场部门',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL),(109,102,'0,100,102','财务部门',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-06-24 11:49:28','',NULL);

/*Table structure for table `sys_dict_data` */

DROP TABLE IF EXISTS `sys_dict_data`;

CREATE TABLE `sys_dict_data` (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='字典数据表';

/*Data for the table `sys_dict_data` */

insert  into `sys_dict_data`(`dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,1,'男','0','sys_user_sex','','','Y','0','admin','2025-06-24 11:49:29','',NULL,'性别男'),(2,2,'女','1','sys_user_sex','','','N','0','admin','2025-06-24 11:49:29','',NULL,'性别女'),(3,3,'未知','2','sys_user_sex','','','N','0','admin','2025-06-24 11:49:29','',NULL,'性别未知'),(4,1,'显示','0','sys_show_hide','','primary','Y','0','admin','2025-06-24 11:49:29','',NULL,'显示菜单'),(5,2,'隐藏','1','sys_show_hide','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'隐藏菜单'),(6,1,'正常','0','sys_normal_disable','','primary','Y','0','admin','2025-06-24 11:49:29','',NULL,'正常状态'),(7,2,'停用','1','sys_normal_disable','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'停用状态'),(8,1,'正常','0','sys_job_status','','primary','Y','0','admin','2025-06-24 11:49:29','',NULL,'正常状态'),(9,2,'暂停','1','sys_job_status','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'停用状态'),(10,1,'默认','DEFAULT','sys_job_group','','','Y','0','admin','2025-06-24 11:49:29','',NULL,'默认分组'),(11,2,'系统','SYSTEM','sys_job_group','','','N','0','admin','2025-06-24 11:49:29','',NULL,'系统分组'),(12,1,'是','Y','sys_yes_no','','primary','Y','0','admin','2025-06-24 11:49:29','',NULL,'系统默认是'),(13,2,'否','N','sys_yes_no','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'系统默认否'),(14,1,'通知','1','sys_notice_type','','warning','Y','0','admin','2025-06-24 11:49:29','',NULL,'通知'),(15,2,'公告','2','sys_notice_type','','success','N','0','admin','2025-06-24 11:49:29','',NULL,'公告'),(16,1,'正常','0','sys_notice_status','','primary','Y','0','admin','2025-06-24 11:49:29','',NULL,'正常状态'),(17,2,'关闭','1','sys_notice_status','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'关闭状态'),(18,99,'其他','0','sys_oper_type','','info','N','0','admin','2025-06-24 11:49:29','',NULL,'其他操作'),(19,1,'新增','1','sys_oper_type','','info','N','0','admin','2025-06-24 11:49:29','',NULL,'新增操作'),(20,2,'修改','2','sys_oper_type','','info','N','0','admin','2025-06-24 11:49:29','',NULL,'修改操作'),(21,3,'删除','3','sys_oper_type','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'删除操作'),(22,4,'授权','4','sys_oper_type','','primary','N','0','admin','2025-06-24 11:49:29','',NULL,'授权操作'),(23,5,'导出','5','sys_oper_type','','warning','N','0','admin','2025-06-24 11:49:29','',NULL,'导出操作'),(24,6,'导入','6','sys_oper_type','','warning','N','0','admin','2025-06-24 11:49:29','',NULL,'导入操作'),(25,7,'强退','7','sys_oper_type','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'强退操作'),(26,8,'生成代码','8','sys_oper_type','','warning','N','0','admin','2025-06-24 11:49:29','',NULL,'生成操作'),(27,9,'清空数据','9','sys_oper_type','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'清空操作'),(28,1,'成功','0','sys_common_status','','primary','N','0','admin','2025-06-24 11:49:29','',NULL,'正常状态'),(29,2,'失败','1','sys_common_status','','danger','N','0','admin','2025-06-24 11:49:29','',NULL,'停用状态');

/*Table structure for table `sys_dict_type` */

DROP TABLE IF EXISTS `sys_dict_type`;

CREATE TABLE `sys_dict_type` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`),
  UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='字典类型表';

/*Data for the table `sys_dict_type` */

insert  into `sys_dict_type`(`dict_id`,`dict_name`,`dict_type`,`status`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,'用户性别','sys_user_sex','0','admin','2025-06-24 11:49:29','',NULL,'用户性别列表'),(2,'菜单状态','sys_show_hide','0','admin','2025-06-24 11:49:29','',NULL,'菜单状态列表'),(3,'系统开关','sys_normal_disable','0','admin','2025-06-24 11:49:29','',NULL,'系统开关列表'),(4,'任务状态','sys_job_status','0','admin','2025-06-24 11:49:29','',NULL,'任务状态列表'),(5,'任务分组','sys_job_group','0','admin','2025-06-24 11:49:29','',NULL,'任务分组列表'),(6,'系统是否','sys_yes_no','0','admin','2025-06-24 11:49:29','',NULL,'系统是否列表'),(7,'通知类型','sys_notice_type','0','admin','2025-06-24 11:49:29','',NULL,'通知类型列表'),(8,'通知状态','sys_notice_status','0','admin','2025-06-24 11:49:29','',NULL,'通知状态列表'),(9,'操作类型','sys_oper_type','0','admin','2025-06-24 11:49:29','',NULL,'操作类型列表'),(10,'系统状态','sys_common_status','0','admin','2025-06-24 11:49:29','',NULL,'登录状态列表');

/*Table structure for table `sys_job` */

DROP TABLE IF EXISTS `sys_job`;

CREATE TABLE `sys_job` (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`,`job_name`,`job_group`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务调度表';

/*Data for the table `sys_job` */

insert  into `sys_job`(`job_id`,`job_name`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,'系统默认（无参）','DEFAULT','ryTask.ryNoParams','0/10 * * * * ?','3','1','1','admin','2025-06-24 11:49:29','',NULL,''),(2,'系统默认（有参）','DEFAULT','ryTask.ryParams(\'ry\')','0/15 * * * * ?','3','1','1','admin','2025-06-24 11:49:29','',NULL,''),(3,'系统默认（多参）','DEFAULT','ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)','0/20 * * * * ?','3','1','1','admin','2025-06-24 11:49:29','',NULL,'');

/*Table structure for table `sys_job_log` */

DROP TABLE IF EXISTS `sys_job_log`;

CREATE TABLE `sys_job_log` (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) DEFAULT NULL COMMENT '日志信息',
  `status` char(1) DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) DEFAULT '' COMMENT '异常信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务调度日志表';

/*Data for the table `sys_job_log` */

/*Table structure for table `sys_logininfor` */

DROP TABLE IF EXISTS `sys_logininfor`;

CREATE TABLE `sys_logininfor` (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT '' COMMENT '操作系统',
  `status` char(1) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) DEFAULT '' COMMENT '提示消息',
  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`),
  KEY `idx_sys_logininfor_s` (`status`),
  KEY `idx_sys_logininfor_lt` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=139 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统访问记录';

/*Data for the table `sys_logininfor` */

insert  into `sys_logininfor`(`info_id`,`user_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`) values (100,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-06-24 15:49:00'),(101,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-01 14:06:35'),(102,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-01 14:06:41'),(103,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-01 14:18:55'),(104,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-01 14:19:05'),(105,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-01 15:28:00'),(106,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-01 15:36:01'),(107,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-01 15:36:05'),(108,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-01 16:45:45'),(109,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-01 16:45:49'),(110,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-01 19:38:56'),(111,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-03 14:22:30'),(112,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-04 10:46:43'),(113,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-04 14:06:30'),(114,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-04 14:06:32'),(115,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-04 14:06:35'),(116,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-04 15:17:38'),(117,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-04 17:11:03'),(118,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-04 17:54:52'),(119,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-04 17:55:03'),(120,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-04 19:45:56'),(121,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-05 10:38:53'),(122,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-05 11:48:09'),(123,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-05 11:48:13'),(124,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-05 15:44:12'),(125,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 14:18:28'),(126,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 16:52:38'),(127,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-07 20:02:38'),(128,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-08 09:08:41'),(129,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-08 09:10:13'),(130,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-08 09:10:15'),(131,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-08 09:10:16'),(132,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-08 09:12:51'),(133,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-08 09:12:54'),(134,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-08 09:13:02'),(135,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-08 09:15:20'),(136,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-08 09:24:10'),(137,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-08 09:24:13'),(138,'admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-08 09:24:16');

/*Table structure for table `sys_menu` */

DROP TABLE IF EXISTS `sys_menu`;

CREATE TABLE `sys_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) DEFAULT '' COMMENT '路由名称',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2043 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='菜单权限表';

/*Data for the table `sys_menu` */

insert  into `sys_menu`(`menu_id`,`menu_name`,`parent_id`,`order_num`,`path`,`component`,`query`,`route_name`,`is_frame`,`is_cache`,`menu_type`,`visible`,`status`,`perms`,`icon`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,'系统管理',0,1,'system',NULL,'','',1,0,'M','0','0','','system','admin','2025-06-24 11:49:28','',NULL,'系统管理目录'),(2,'系统监控',0,2,'monitor',NULL,'','',1,0,'M','0','0','','monitor','admin','2025-06-24 11:49:28','',NULL,'系统监控目录'),(3,'系统工具',0,3,'tool',NULL,'','',1,0,'M','0','0','','tool','admin','2025-06-24 11:49:28','',NULL,'系统工具目录'),(4,'若依官网',0,4,'http://ruoyi.vip',NULL,'','',0,0,'M','0','0','','guide','admin','2025-06-24 11:49:28','',NULL,'若依官网地址'),(100,'用户管理',1,1,'user','system/user/index','','',1,0,'C','0','0','system:user:list','user','admin','2025-06-24 11:49:28','',NULL,'用户管理菜单'),(101,'角色管理',1,2,'role','system/role/index','','',1,0,'C','0','0','system:role:list','peoples','admin','2025-06-24 11:49:28','',NULL,'角色管理菜单'),(102,'菜单管理',1,3,'menu','system/menu/index','','',1,0,'C','0','0','system:menu:list','tree-table','admin','2025-06-24 11:49:28','',NULL,'菜单管理菜单'),(103,'部门管理',1,4,'dept','system/dept/index','','',1,0,'C','0','0','system:dept:list','tree','admin','2025-06-24 11:49:28','',NULL,'部门管理菜单'),(104,'岗位管理',1,5,'post','system/post/index','','',1,0,'C','0','0','system:post:list','post','admin','2025-06-24 11:49:28','',NULL,'岗位管理菜单'),(105,'字典管理',1,6,'dict','system/dict/index','','',1,0,'C','0','0','system:dict:list','dict','admin','2025-06-24 11:49:28','',NULL,'字典管理菜单'),(106,'参数设置',1,7,'config','system/config/index','','',1,0,'C','0','0','system:config:list','edit','admin','2025-06-24 11:49:28','',NULL,'参数设置菜单'),(107,'通知公告',1,8,'notice','system/notice/index','','',1,0,'C','0','0','system:notice:list','message','admin','2025-06-24 11:49:28','',NULL,'通知公告菜单'),(108,'日志管理',1,9,'log','','','',1,0,'M','0','0','','log','admin','2025-06-24 11:49:28','',NULL,'日志管理菜单'),(109,'在线用户',2,1,'online','monitor/online/index','','',1,0,'C','0','0','monitor:online:list','online','admin','2025-06-24 11:49:28','',NULL,'在线用户菜单'),(110,'定时任务',2,2,'job','monitor/job/index','','',1,0,'C','0','0','monitor:job:list','job','admin','2025-06-24 11:49:28','',NULL,'定时任务菜单'),(111,'数据监控',2,3,'druid','monitor/druid/index','','',1,0,'C','0','0','monitor:druid:list','druid','admin','2025-06-24 11:49:28','',NULL,'数据监控菜单'),(112,'服务监控',2,4,'server','monitor/server/index','','',1,0,'C','0','0','monitor:server:list','server','admin','2025-06-24 11:49:28','',NULL,'服务监控菜单'),(113,'缓存监控',2,5,'cache','monitor/cache/index','','',1,0,'C','0','0','monitor:cache:list','redis','admin','2025-06-24 11:49:28','',NULL,'缓存监控菜单'),(114,'缓存列表',2,6,'cacheList','monitor/cache/list','','',1,0,'C','0','0','monitor:cache:list','redis-list','admin','2025-06-24 11:49:28','',NULL,'缓存列表菜单'),(115,'表单构建',3,1,'build','tool/build/index','','',1,0,'C','0','0','tool:build:list','build','admin','2025-06-24 11:49:28','',NULL,'表单构建菜单'),(116,'代码生成',3,2,'gen','tool/gen/index','','',1,0,'C','0','0','tool:gen:list','code','admin','2025-06-24 11:49:28','',NULL,'代码生成菜单'),(117,'系统接口',3,3,'swagger','tool/swagger/index','','',1,0,'C','0','0','tool:swagger:list','swagger','admin','2025-06-24 11:49:28','',NULL,'系统接口菜单'),(500,'操作日志',108,1,'operlog','monitor/operlog/index','','',1,0,'C','0','0','monitor:operlog:list','form','admin','2025-06-24 11:49:28','',NULL,'操作日志菜单'),(501,'登录日志',108,2,'logininfor','monitor/logininfor/index','','',1,0,'C','0','0','monitor:logininfor:list','logininfor','admin','2025-06-24 11:49:28','',NULL,'登录日志菜单'),(1000,'用户查询',100,1,'','','','',1,0,'F','0','0','system:user:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1001,'用户新增',100,2,'','','','',1,0,'F','0','0','system:user:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1002,'用户修改',100,3,'','','','',1,0,'F','0','0','system:user:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1003,'用户删除',100,4,'','','','',1,0,'F','0','0','system:user:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1004,'用户导出',100,5,'','','','',1,0,'F','0','0','system:user:export','#','admin','2025-06-24 11:49:28','',NULL,''),(1005,'用户导入',100,6,'','','','',1,0,'F','0','0','system:user:import','#','admin','2025-06-24 11:49:28','',NULL,''),(1006,'重置密码',100,7,'','','','',1,0,'F','0','0','system:user:resetPwd','#','admin','2025-06-24 11:49:28','',NULL,''),(1007,'角色查询',101,1,'','','','',1,0,'F','0','0','system:role:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1008,'角色新增',101,2,'','','','',1,0,'F','0','0','system:role:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1009,'角色修改',101,3,'','','','',1,0,'F','0','0','system:role:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1010,'角色删除',101,4,'','','','',1,0,'F','0','0','system:role:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1011,'角色导出',101,5,'','','','',1,0,'F','0','0','system:role:export','#','admin','2025-06-24 11:49:28','',NULL,''),(1012,'菜单查询',102,1,'','','','',1,0,'F','0','0','system:menu:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1013,'菜单新增',102,2,'','','','',1,0,'F','0','0','system:menu:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1014,'菜单修改',102,3,'','','','',1,0,'F','0','0','system:menu:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1015,'菜单删除',102,4,'','','','',1,0,'F','0','0','system:menu:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1016,'部门查询',103,1,'','','','',1,0,'F','0','0','system:dept:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1017,'部门新增',103,2,'','','','',1,0,'F','0','0','system:dept:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1018,'部门修改',103,3,'','','','',1,0,'F','0','0','system:dept:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1019,'部门删除',103,4,'','','','',1,0,'F','0','0','system:dept:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1020,'岗位查询',104,1,'','','','',1,0,'F','0','0','system:post:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1021,'岗位新增',104,2,'','','','',1,0,'F','0','0','system:post:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1022,'岗位修改',104,3,'','','','',1,0,'F','0','0','system:post:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1023,'岗位删除',104,4,'','','','',1,0,'F','0','0','system:post:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1024,'岗位导出',104,5,'','','','',1,0,'F','0','0','system:post:export','#','admin','2025-06-24 11:49:28','',NULL,''),(1025,'字典查询',105,1,'#','','','',1,0,'F','0','0','system:dict:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1026,'字典新增',105,2,'#','','','',1,0,'F','0','0','system:dict:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1027,'字典修改',105,3,'#','','','',1,0,'F','0','0','system:dict:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1028,'字典删除',105,4,'#','','','',1,0,'F','0','0','system:dict:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1029,'字典导出',105,5,'#','','','',1,0,'F','0','0','system:dict:export','#','admin','2025-06-24 11:49:28','',NULL,''),(1030,'参数查询',106,1,'#','','','',1,0,'F','0','0','system:config:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1031,'参数新增',106,2,'#','','','',1,0,'F','0','0','system:config:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1032,'参数修改',106,3,'#','','','',1,0,'F','0','0','system:config:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1033,'参数删除',106,4,'#','','','',1,0,'F','0','0','system:config:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1034,'参数导出',106,5,'#','','','',1,0,'F','0','0','system:config:export','#','admin','2025-06-24 11:49:28','',NULL,''),(1035,'公告查询',107,1,'#','','','',1,0,'F','0','0','system:notice:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1036,'公告新增',107,2,'#','','','',1,0,'F','0','0','system:notice:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1037,'公告修改',107,3,'#','','','',1,0,'F','0','0','system:notice:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1038,'公告删除',107,4,'#','','','',1,0,'F','0','0','system:notice:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1039,'操作查询',500,1,'#','','','',1,0,'F','0','0','monitor:operlog:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1040,'操作删除',500,2,'#','','','',1,0,'F','0','0','monitor:operlog:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1041,'日志导出',500,3,'#','','','',1,0,'F','0','0','monitor:operlog:export','#','admin','2025-06-24 11:49:28','',NULL,''),(1042,'登录查询',501,1,'#','','','',1,0,'F','0','0','monitor:logininfor:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1043,'登录删除',501,2,'#','','','',1,0,'F','0','0','monitor:logininfor:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1044,'日志导出',501,3,'#','','','',1,0,'F','0','0','monitor:logininfor:export','#','admin','2025-06-24 11:49:28','',NULL,''),(1045,'账户解锁',501,4,'#','','','',1,0,'F','0','0','monitor:logininfor:unlock','#','admin','2025-06-24 11:49:28','',NULL,''),(1046,'在线查询',109,1,'#','','','',1,0,'F','0','0','monitor:online:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1047,'批量强退',109,2,'#','','','',1,0,'F','0','0','monitor:online:batchLogout','#','admin','2025-06-24 11:49:28','',NULL,''),(1048,'单条强退',109,3,'#','','','',1,0,'F','0','0','monitor:online:forceLogout','#','admin','2025-06-24 11:49:28','',NULL,''),(1049,'任务查询',110,1,'#','','','',1,0,'F','0','0','monitor:job:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1050,'任务新增',110,2,'#','','','',1,0,'F','0','0','monitor:job:add','#','admin','2025-06-24 11:49:28','',NULL,''),(1051,'任务修改',110,3,'#','','','',1,0,'F','0','0','monitor:job:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1052,'任务删除',110,4,'#','','','',1,0,'F','0','0','monitor:job:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1053,'状态修改',110,5,'#','','','',1,0,'F','0','0','monitor:job:changeStatus','#','admin','2025-06-24 11:49:28','',NULL,''),(1054,'任务导出',110,6,'#','','','',1,0,'F','0','0','monitor:job:export','#','admin','2025-06-24 11:49:28','',NULL,''),(1055,'生成查询',116,1,'#','','','',1,0,'F','0','0','tool:gen:query','#','admin','2025-06-24 11:49:28','',NULL,''),(1056,'生成修改',116,2,'#','','','',1,0,'F','0','0','tool:gen:edit','#','admin','2025-06-24 11:49:28','',NULL,''),(1057,'生成删除',116,3,'#','','','',1,0,'F','0','0','tool:gen:remove','#','admin','2025-06-24 11:49:28','',NULL,''),(1058,'导入代码',116,4,'#','','','',1,0,'F','0','0','tool:gen:import','#','admin','2025-06-24 11:49:28','',NULL,''),(1059,'预览代码',116,5,'#','','','',1,0,'F','0','0','tool:gen:preview','#','admin','2025-06-24 11:49:28','',NULL,''),(1060,'生成代码',116,6,'#','','','',1,0,'F','0','0','tool:gen:code','#','admin','2025-06-24 11:49:28','',NULL,''),(2000,'医院信息',0,3,'hospital',NULL,NULL,'',1,0,'M','0','0','','peoples','admin','2025-07-01 14:37:36','admin','2025-07-01 16:29:13',''),(2001,'预约订单',3,1,'appointments','system/appointments/index',NULL,'',1,0,'C','0','0','system:appointments:list','#','admin','2025-07-01 14:57:07','',NULL,'预约订单菜单'),(2002,'预约订单查询',2001,1,'#','',NULL,'',1,0,'F','0','0','system:appointments:query','#','admin','2025-07-01 14:57:07','',NULL,''),(2003,'预约订单新增',2001,2,'#','',NULL,'',1,0,'F','0','0','system:appointments:add','#','admin','2025-07-01 14:57:07','',NULL,''),(2004,'预约订单修改',2001,3,'#','',NULL,'',1,0,'F','0','0','system:appointments:edit','#','admin','2025-07-01 14:57:07','',NULL,''),(2005,'预约订单删除',2001,4,'#','',NULL,'',1,0,'F','0','0','system:appointments:remove','#','admin','2025-07-01 14:57:07','',NULL,''),(2006,'预约订单导出',2001,5,'#','',NULL,'',1,0,'F','0','0','system:appointments:export','#','admin','2025-07-01 14:57:07','',NULL,''),(2007,'预约管理',2000,1,'appointment','system/hospital/appointment',NULL,'',1,1,'C','0','0','','code','admin','2025-07-01 15:34:43','admin','2025-07-01 16:09:49',''),(2008,'医院信息',3,1,'hospitals','system/hospitals/index',NULL,'',1,0,'C','0','0','system:hospitals:list','#','admin','2025-07-01 17:30:31','',NULL,'医院信息菜单'),(2009,'医院信息查询',2008,1,'#','',NULL,'',1,0,'F','0','0','system:hospitals:query','#','admin','2025-07-01 17:30:32','',NULL,''),(2010,'医院信息新增',2008,2,'#','',NULL,'',1,0,'F','0','0','system:hospitals:add','#','admin','2025-07-01 17:30:32','',NULL,''),(2011,'医院信息修改',2008,3,'#','',NULL,'',1,0,'F','0','0','system:hospitals:edit','#','admin','2025-07-01 17:30:32','',NULL,''),(2012,'医院信息删除',2008,4,'#','',NULL,'',1,0,'F','0','0','system:hospitals:remove','#','admin','2025-07-01 17:30:32','',NULL,''),(2013,'医院信息导出',2008,5,'#','',NULL,'',1,0,'F','0','0','system:hospitals:export','#','admin','2025-07-01 17:30:32','',NULL,''),(2014,'科室信息',3,1,'departments','system/departments/index',NULL,'',1,0,'C','0','0','system:departments:list','#','admin','2025-07-01 19:44:14','',NULL,'科室信息菜单'),(2015,'科室信息查询',2014,1,'#','',NULL,'',1,0,'F','0','0','system:departments:query','#','admin','2025-07-01 19:44:15','',NULL,''),(2016,'科室信息新增',2014,2,'#','',NULL,'',1,0,'F','0','0','system:departments:add','#','admin','2025-07-01 19:44:15','',NULL,''),(2017,'科室信息修改',2014,3,'#','',NULL,'',1,0,'F','0','0','system:departments:edit','#','admin','2025-07-01 19:44:15','',NULL,''),(2018,'科室信息删除',2014,4,'#','',NULL,'',1,0,'F','0','0','system:departments:remove','#','admin','2025-07-01 19:44:15','',NULL,''),(2019,'科室信息导出',2014,5,'#','',NULL,'',1,0,'F','0','0','system:departments:export','#','admin','2025-07-01 19:44:15','',NULL,''),(2020,'医生信息',3,1,'doctors','system/doctors/index',NULL,'',1,0,'C','0','0','system:doctors:list','#','admin','2025-07-01 19:45:49','',NULL,'医生信息菜单'),(2021,'医生信息查询',2020,1,'#','',NULL,'',1,0,'F','0','0','system:doctors:query','#','admin','2025-07-01 19:45:49','',NULL,''),(2022,'医生信息新增',2020,2,'#','',NULL,'',1,0,'F','0','0','system:doctors:add','#','admin','2025-07-01 19:45:49','',NULL,''),(2023,'医生信息修改',2020,3,'#','',NULL,'',1,0,'F','0','0','system:doctors:edit','#','admin','2025-07-01 19:45:49','',NULL,''),(2024,'医生信息删除',2020,4,'#','',NULL,'',1,0,'F','0','0','system:doctors:remove','#','admin','2025-07-01 19:45:49','',NULL,''),(2025,'医生信息导出',2020,5,'#','',NULL,'',1,0,'F','0','0','system:doctors:export','#','admin','2025-07-01 19:45:49','',NULL,''),(2026,'就诊记录',3,1,'records','system/records/index',NULL,'',1,0,'C','0','0','system:records:list','#','admin','2025-07-01 19:49:55','',NULL,'就诊记录菜单'),(2027,'就诊记录查询',2026,1,'#','',NULL,'',1,0,'F','0','0','system:records:query','#','admin','2025-07-01 19:49:56','',NULL,''),(2028,'就诊记录新增',2026,2,'#','',NULL,'',1,0,'F','0','0','system:records:add','#','admin','2025-07-01 19:49:56','',NULL,''),(2029,'就诊记录修改',2026,3,'#','',NULL,'',1,0,'F','0','0','system:records:edit','#','admin','2025-07-01 19:49:56','',NULL,''),(2030,'就诊记录删除',2026,4,'#','',NULL,'',1,0,'F','0','0','system:records:remove','#','admin','2025-07-01 19:49:56','',NULL,''),(2031,'就诊记录导出',2026,5,'#','',NULL,'',1,0,'F','0','0','system:records:export','#','admin','2025-07-01 19:49:56','',NULL,''),(2032,'医生排班',3,1,'schedules','system/schedules/index',NULL,'',1,0,'C','0','0','system:schedules:list','#','admin','2025-07-01 19:52:41','',NULL,'医生排班菜单'),(2033,'医生排班查询',2032,1,'#','',NULL,'',1,0,'F','0','0','system:schedules:query','#','admin','2025-07-01 19:52:41','',NULL,''),(2034,'医生排班新增',2032,2,'#','',NULL,'',1,0,'F','0','0','system:schedules:add','#','admin','2025-07-01 19:52:41','',NULL,''),(2035,'医生排班修改',2032,3,'#','',NULL,'',1,0,'F','0','0','system:schedules:edit','#','admin','2025-07-01 19:52:41','',NULL,''),(2036,'医生排班删除',2032,4,'#','',NULL,'',1,0,'F','0','0','system:schedules:remove','#','admin','2025-07-01 19:52:41','',NULL,''),(2037,'医生排班导出',2032,5,'#','',NULL,'',1,0,'F','0','0','system:schedules:export','#','admin','2025-07-01 19:52:41','',NULL,''),(2038,'科室信息',2000,2,'departments','system/hospital/departments',NULL,'hospital',1,1,'C','0','0','','code','admin','2025-07-01 19:59:44','admin','2025-07-07 15:42:05',''),(2039,'医生信息',2000,3,'doctors','system/hospital/doctors',NULL,'',1,1,'C','0','0','','code','admin','2025-07-01 20:00:28','admin','2025-07-07 14:19:22',''),(2040,'医院信息',2000,4,'hospitals','system/hospital/hospitals',NULL,'',1,1,'C','0','0','','code','admin','2025-07-01 20:00:59','admin','2025-07-07 14:19:28',''),(2041,'记录信息',2000,5,'records','system/hospital/records',NULL,'',1,1,'C','0','0','','code','admin','2025-07-01 20:01:42','admin','2025-07-07 14:19:33',''),(2042,'医生排班',2000,6,'schedules','system/hospital/schedules',NULL,'',1,1,'C','0','0','','code','admin','2025-07-01 20:02:39','admin','2025-07-07 16:03:59','');

/*Table structure for table `sys_notice` */

DROP TABLE IF EXISTS `sys_notice`;

CREATE TABLE `sys_notice` (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) NOT NULL COMMENT '公告标题',
  `notice_type` char(1) NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知公告表';

/*Data for the table `sys_notice` */

insert  into `sys_notice`(`notice_id`,`notice_title`,`notice_type`,`notice_content`,`status`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,'温馨提醒：2018-07-01 若依新版本发布啦','2','新版本内容','0','admin','2025-06-24 11:49:29','',NULL,'管理员'),(2,'维护通知：2018-07-01 若依系统凌晨维护','1','维护内容','0','admin','2025-06-24 11:49:29','',NULL,'管理员');

/*Table structure for table `sys_oper_log` */

DROP TABLE IF EXISTS `sys_oper_log`;

CREATE TABLE `sys_oper_log` (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) DEFAULT '' COMMENT '模块标题',
  `business_type` int DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) DEFAULT '' COMMENT '请求方式',
  `operator_type` int DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) DEFAULT '' COMMENT '返回参数',
  `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint DEFAULT '0' COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`),
  KEY `idx_sys_oper_log_bt` (`business_type`),
  KEY `idx_sys_oper_log_s` (`status`),
  KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=210 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志记录';

/*Data for the table `sys_oper_log` */

insert  into `sys_oper_log`(`oper_id`,`title`,`business_type`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`json_result`,`status`,`error_msg`,`oper_time`,`cost_time`) values (100,'代码生成',6,'com.ruoyi.generator.controller.GenController.importTableSave()','POST',1,'admin','研发部门','/tool/gen/importTable','127.0.0.1','内网IP','{\"tables\":\"hospitals\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 14:29:23',218),(101,'代码生成',6,'com.ruoyi.generator.controller.GenController.importTableSave()','POST',1,'admin','研发部门','/tool/gen/importTable','127.0.0.1','内网IP','{\"tables\":\"appointments,departments,doctors,medical_records,schedules\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 14:36:21',215),(102,'代码生成',8,'com.ruoyi.generator.controller.GenController.batchGenCode()','GET',1,'admin','研发部门','/tool/gen/batchGenCode','127.0.0.1','内网IP','{\"tables\":\"appointments\"}',NULL,0,NULL,'2025-07-01 14:36:28',112),(103,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"医院信息\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"hospital\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 14:37:36',29),(104,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/appointment\",\"createBy\":\"admin\",\"icon\":\"code\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"预约管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"hospital\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 15:34:43',23),(105,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/appointment\",\"createTime\":\"2025-07-01 15:34:43\",\"icon\":\"code\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"预约管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"appointment\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 16:03:52',57),(106,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/appointment/index\",\"createTime\":\"2025-07-01 15:34:43\",\"icon\":\"code\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"预约管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"appointment\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 16:05:13',18),(107,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/appointment\",\"createTime\":\"2025-07-01 15:34:43\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2007,\"menuName\":\"预约管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"appointment\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 16:09:49',9),(108,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-07-01 14:37:36\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"医院信息\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 16:28:21',69),(109,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-07-01 14:37:36\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"医院信息\",\"menuType\":\"M\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"hospital\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 16:29:13',17),(110,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/departments\",\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"部门信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"departments\",\"routeName\":\"hospital\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 19:59:44',129),(111,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/doctors\",\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"医生信息\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2000,\"path\":\"doctors\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 20:00:28',12),(112,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/hospitals\",\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"医院信息\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2000,\"path\":\"hospitals\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 20:00:59',12),(113,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/records\",\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"记录信息\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2000,\"path\":\"records\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 20:01:42',23),(114,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/departments\",\"createTime\":\"2025-07-01 19:59:44\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2038,\"menuName\":\"部门信息\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2000,\"path\":\"departments\",\"perms\":\"\",\"routeName\":\"hospital\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 20:01:57',28),(115,'菜单管理',1,'com.ruoyi.web.controller.system.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/scheduls\",\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"日期安排\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"scheduls\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 20:02:39',21),(116,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/schedules\",\"createTime\":\"2025-07-01 20:02:39\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2042,\"menuName\":\"日期安排\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"schedules\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 20:17:35',28),(117,'医院信息',1,'com.ruoyi.hospital.controller.HospitalsController.add()','POST',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"北京\",\"createTime\":\"2025-07-01 20:20:22\",\"description\":\"是不是4 + 4 啊\",\"hospitalId\":1,\"hospitalName\":\"协和医院\",\"params\":{},\"phone\":\"15533681891\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-01 20:20:22',21),(118,'科室信息',1,'com.ruoyi.hospital.controller.DepartmentsController.add()','POST',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-03 14:25:07\",\"deptId\":1,\"deptName\":\"眼科\",\"description\":\"看眼的，脑子不好别来\",\"hospitalId\":1,\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-03 14:25:07',22),(119,'医生信息',1,'com.ruoyi.hospital.controller.DoctorsController.add()','POST',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":200,\"createTime\":\"2025-07-03 14:26:28\",\"deptId\":1,\"doctorId\":1,\"introduction\":\"老中医了\",\"params\":{},\"specialty\":\"眼部疾病\",\"workYears\":666}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-03 14:26:28',25),(120,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/schedules\",\"createTime\":\"2025-07-01 20:02:39\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2042,\"menuName\":\"医生排班\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"schedules\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-03 14:30:52',43),(121,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"createTime\":\"2025-07-03 14:35:40\",\"date\":\"2025-7-3\",\"doctorId\":1,\"morningStart\":\"2025-07-03 07:00\",\"params\":{},\"scheduleId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-03 14:35:40',38),(122,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"createTime\":\"2025-07-03 14:35:58\",\"date\":\"2025-07-03\",\"doctorId\":1,\"morningStart\":\"2025-07-03 00:00\",\"params\":{},\"scheduleId\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-03 14:35:58',16),(123,'医生排班',2,'com.ruoyi.hospital.controller.SchedulesController.edit()','PUT',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonQuota\":0,\"afternoonRemaining\":0,\"createTime\":\"2025-07-03 14:35:40\",\"date\":\"2025-7-3\",\"deleted\":0,\"doctorId\":1,\"morningQuota\":0,\"morningRemaining\":0,\"morningStart\":\"2025-07-03 15:04\",\"nightQuota\":0,\"nightRemaining\":0,\"params\":{},\"scheduleId\":1,\"updateTime\":\"2025-07-03 15:05:37\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-03 15:05:37',23),(124,'医生排班',2,'com.ruoyi.hospital.controller.SchedulesController.edit()','PUT',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonQuota\":0,\"afternoonRemaining\":0,\"createTime\":\"2025-07-03 14:35:40\",\"date\":\"2025-07-03\",\"deleted\":0,\"doctorId\":1,\"morningQuota\":0,\"morningRemaining\":0,\"morningStart\":\"2025-07-03 15:04\",\"nightQuota\":0,\"nightRemaining\":0,\"params\":{},\"scheduleId\":1,\"updateTime\":\"2025-07-03 15:06:07\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-03 15:06:07',9),(125,'医生排班',2,'com.ruoyi.hospital.controller.SchedulesController.edit()','PUT',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"2025-07-03 17:09\",\"afternoonQuota\":1,\"afternoonRemaining\":1,\"afternoonStart\":\"2025-07-03 15:09\",\"createTime\":\"2025-07-03 14:35:40\",\"date\":\"2025-07-03\",\"deleted\":0,\"doctorId\":1,\"morningEnd\":\"2025-07-03 10:08\",\"morningQuota\":2,\"morningRemaining\":2,\"morningStart\":\"2025-07-03 07:04\",\"nightEnd\":\"2025-07-03\",\"nightQuota\":1,\"nightRemaining\":2,\"nightStart\":\"2025-07-03\",\"params\":{},\"scheduleId\":1,\"updateTime\":\"2025-07-03 15:09:53\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-03 15:09:53',13),(126,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-03 14:25:07\",\"deleted\":0,\"deptId\":1,\"deptName\":\"眼科\",\"description\":\"看眼的，脑子不好别来，真的\",\"hospitalId\":1,\"params\":{},\"updateTime\":\"2025-07-04 11:16:09\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 11:16:09',120),(127,'医生排班',2,'com.ruoyi.hospital.controller.SchedulesController.edit()','PUT',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonQuota\":0,\"afternoonRemaining\":0,\"createTime\":\"2025-07-03 14:35:58\",\"date\":\"2025-07-03\",\"deleted\":0,\"doctorId\":1,\"morningEnd\":\"2025-07-10 05:00\",\"morningQuota\":0,\"morningRemaining\":0,\"morningStart\":\"2025-07-03 03:15\",\"nightQuota\":0,\"nightRemaining\":0,\"params\":{},\"scheduleId\":2,\"updateTime\":\"2025-07-04 11:38:14\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 11:38:14',119),(128,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/schedules\",\"createTime\":\"2025-07-01 20:02:39\",\"icon\":\"#\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2042,\"menuName\":\"医生排班\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"schedules\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 15:21:21',32),(129,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/records\",\"createTime\":\"2025-07-01 20:01:42\",\"icon\":\"#\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2041,\"menuName\":\"记录信息\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2000,\"path\":\"records\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 15:21:27',13),(130,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/hospitals\",\"createTime\":\"2025-07-01 20:00:59\",\"icon\":\"#\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2040,\"menuName\":\"医院信息\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2000,\"path\":\"hospitals\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 15:21:31',12),(131,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/doctors\",\"createTime\":\"2025-07-01 20:00:28\",\"icon\":\"#\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2039,\"menuName\":\"医生信息\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2000,\"path\":\"doctors\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 15:21:34',15),(132,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/departments\",\"createTime\":\"2025-07-01 19:59:44\",\"icon\":\"#\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2038,\"menuName\":\"部门信息\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2000,\"path\":\"departments\",\"perms\":\"\",\"routeName\":\"hospital\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 15:21:38',12),(133,'医生信息',1,'com.ruoyi.hospital.controller.DoctorsController.add()','POST',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":150,\"createTime\":\"2025-07-04 16:35:22\",\"deptId\":1,\"doctorId\":3,\"introduction\":\"333\",\"params\":{},\"specialty\":\"老寒腿\",\"workYears\":5}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 16:35:22',28),(134,'科室信息',1,'com.ruoyi.hospital.controller.DepartmentsController.add()','POST',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-04 17:34:47\",\"deleted\":0,\"deptId\":2,\"deptName\":\"2\",\"description\":\"疑难杂症可来\",\"hospitalId\":1,\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 17:34:47',149),(135,'医生信息',1,'com.ruoyi.hospital.controller.DoctorsController.add()','POST',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":500,\"createTime\":\"2025-07-04 17:37:16\",\"deleted\":0,\"deptId\":2,\"doctorId\":4,\"introduction\":\"一路拉风带神殿\",\"params\":{},\"specialty\":\"临床医学\",\"title\":\"住院医师\",\"workYears\":20}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 17:37:16',12),(136,'医生信息',1,'com.ruoyi.hospital.controller.DoctorsController.add()','POST',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":502,\"createTime\":\"2025-07-04 17:39:34\",\"deptId\":2,\"doctorId\":5,\"doctorName\":\"Smisdd Ma\",\"introduction\":\"房贷首付fess帅哥帅哥如果如果\",\"params\":{},\"specialty\":\"你猜\",\"title\":\"主治医师\",\"workYears\":25}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 17:39:34',18),(137,'医生信息',1,'com.ruoyi.hospital.controller.DoctorsController.add()','POST',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":250,\"createTime\":\"2025-07-04 17:45:06\",\"deptId\":2,\"doctorId\":6,\"doctorName\":\"mjkworld\",\"introduction\":\"你问我，我问AI\",\"params\":{},\"specialty\":\"AI看病\",\"title\":\"主任医师\",\"workYears\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 17:45:06',14),(138,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"18:49\",\"afternoonRemaining\":10,\"afternoonStart\":\"14:49\",\"createTime\":\"2025-07-04 20:52:12\",\"date\":\"2025-07-07\",\"doctorName\":\"3\",\"morningEnd\":\"11:49\",\"morningRemaining\":5,\"morningStart\":\"08:49\",\"nightEnd\":\"22:49\",\"nightRemaining\":3,\"nightStart\":\"20:49\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect datetime value: \'08:49:00\' for column \'morning_start\' at row 1\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\SchedulesMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.SchedulesMapper.insertSchedules-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into schedules          ( date,             morning_start,             morning_end,             afternoon_start,             afternoon_end,             night_start,             night_end,                                                    morning_remaining,             afternoon_remaining,             night_remaining,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ?,             ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect datetime value: \'08:49:00\' for column \'morning_start\' at row 1\n; Data truncation: Incorrect datetime value: \'08:49:00\' for column \'morning_start\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect datetime value: \'08:49:00\' for column \'morning_start\' at row 1','2025-07-04 20:52:12',87),(139,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"20:56\",\"afternoonRemaining\":5,\"afternoonStart\":\"20:56\",\"createTime\":\"2025-07-04 20:56:24\",\"date\":\"2025-07-06\",\"doctorName\":\"3\",\"morningEnd\":\"20:56\",\"morningRemaining\":2,\"morningStart\":\"20:56\",\"nightEnd\":\"20:56\",\"nightRemaining\":24,\"nightStart\":\"20:56\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect datetime value: \'20:56:00\' for column \'morning_start\' at row 1\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\SchedulesMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.SchedulesMapper.insertSchedules-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into schedules          ( date,             morning_start,             morning_end,             afternoon_start,             afternoon_end,             night_start,             night_end,                                                    morning_remaining,             afternoon_remaining,             night_remaining,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ?,             ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect datetime value: \'20:56:00\' for column \'morning_start\' at row 1\n; Data truncation: Incorrect datetime value: \'20:56:00\' for column \'morning_start\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect datetime value: \'20:56:00\' for column \'morning_start\' at row 1','2025-07-04 20:56:24',7),(140,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"20:56\",\"afternoonRemaining\":5,\"afternoonStart\":\"20:56\",\"createTime\":\"2025-07-04 20:57:27\",\"date\":\"2025-07-06\",\"doctorName\":\"3\",\"morningEnd\":\"20:56\",\"morningRemaining\":2,\"morningStart\":\"20:56\",\"nightEnd\":\"20:56\",\"nightRemaining\":24,\"nightStart\":\"20:56\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\SchedulesMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.SchedulesMapper.insertSchedules-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into schedules          ( date,             morning_start,             morning_end,             afternoon_start,             afternoon_end,             night_start,             night_end,                                                    morning_remaining,             afternoon_remaining,             night_remaining,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ?,             ? )\r\n### Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\n; Field \'doctor_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value','2025-07-04 20:57:27',13),(141,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"20:56\",\"afternoonRemaining\":5,\"afternoonStart\":\"20:56\",\"createTime\":\"2025-07-04 20:57:41\",\"date\":\"2025-07-06\",\"doctorName\":\"3\",\"morningEnd\":\"20:56\",\"morningRemaining\":2,\"morningStart\":\"20:56\",\"nightEnd\":\"20:56\",\"nightRemaining\":24,\"nightStart\":\"20:56\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\SchedulesMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.SchedulesMapper.insertSchedules-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into schedules          ( date,             morning_start,             morning_end,             afternoon_start,             afternoon_end,             night_start,             night_end,                                                    morning_remaining,             afternoon_remaining,             night_remaining,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ?,             ? )\r\n### Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\n; Field \'doctor_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value','2025-07-04 20:57:41',4),(142,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"20:56\",\"afternoonRemaining\":5,\"afternoonStart\":\"20:56\",\"createTime\":\"2025-07-04 20:57:45\",\"date\":\"2025-07-06\",\"doctorName\":\"3\",\"morningEnd\":\"20:56\",\"morningRemaining\":2,\"morningStart\":\"20:56\",\"nightEnd\":\"20:56\",\"nightRemaining\":24,\"nightStart\":\"20:56\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\SchedulesMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.SchedulesMapper.insertSchedules-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into schedules          ( date,             morning_start,             morning_end,             afternoon_start,             afternoon_end,             night_start,             night_end,                                                    morning_remaining,             afternoon_remaining,             night_remaining,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ?,             ? )\r\n### Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\n; Field \'doctor_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value','2025-07-04 20:57:45',5),(143,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"20:56\",\"afternoonRemaining\":5,\"afternoonStart\":\"20:56\",\"createTime\":\"2025-07-04 21:02:44\",\"date\":\"2025-07-06\",\"doctorName\":\"3\",\"morningEnd\":\"20:56\",\"morningRemaining\":2,\"morningStart\":\"20:56\",\"nightEnd\":\"20:56\",\"nightRemaining\":24,\"nightStart\":\"20:56\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\SchedulesMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.SchedulesMapper.insertSchedules-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into schedules          ( date,             morning_start,             morning_end,             afternoon_start,             afternoon_end,             night_start,             night_end,                                                    morning_remaining,             afternoon_remaining,             night_remaining,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ?,             ? )\r\n### Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\n; Field \'doctor_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value','2025-07-04 21:02:44',12),(144,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"21:05\",\"afternoonRemaining\":25,\"afternoonStart\":\"21:05\",\"createTime\":\"2025-07-04 21:05:11\",\"date\":\"2025-07-21\",\"doctorName\":\"王五\",\"morningEnd\":\"21:05\",\"morningRemaining\":2,\"morningStart\":\"21:04\",\"nightEnd\":\"21:05\",\"nightRemaining\":25,\"nightStart\":\"21:05\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\SchedulesMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.SchedulesMapper.insertSchedules-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into schedules          ( date,             morning_start,             morning_end,             afternoon_start,             afternoon_end,             night_start,             night_end,                                                    morning_remaining,             afternoon_remaining,             night_remaining,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ?,             ? )\r\n### Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\n; Field \'doctor_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value','2025-07-04 21:05:11',9),(145,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"21:06\",\"afternoonRemaining\":254,\"afternoonStart\":\"21:06\",\"createTime\":\"2025-07-04 21:06:50\",\"date\":\"2025-07-30\",\"doctorName\":\"王五\",\"morningEnd\":\"21:06\",\"morningRemaining\":25,\"morningStart\":\"21:06\",\"nightEnd\":\"21:06\",\"nightRemaining\":25,\"nightStart\":\"21:06\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\SchedulesMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.SchedulesMapper.insertSchedules-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into schedules          ( date,             morning_start,             morning_end,             afternoon_start,             afternoon_end,             night_start,             night_end,                                                    morning_remaining,             afternoon_remaining,             night_remaining,             create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,             ?,                                                    ?,             ?,             ?,             ? )\r\n### Cause: java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value\n; Field \'doctor_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'doctor_id\' doesn\'t have a default value','2025-07-04 21:06:50',9),(146,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonRemaining\":25,\"createTime\":\"2025-07-04 21:12:16\",\"date\":\"2025-07-21\",\"doctorId\":3,\"doctorName\":\"王五\",\"morningEnd\":\"21:12\",\"morningRemaining\":2,\"morningStart\":\"21:12\",\"nightRemaining\":25,\"params\":{},\"scheduleId\":3}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-04 21:12:16',100),(147,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-04 17:34:47\",\"deleted\":0,\"deptId\":2,\"deptName\":\"疑难杂症科\",\"description\":\"疑难杂症可来\",\"hospitalId\":1,\"params\":{},\"updateTime\":\"2025-07-05 10:40:14\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 10:40:14',21),(148,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-05 10:52:57\",\"deptName\":\"xs\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\AppointmentsMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.AppointmentsMapper.insertAppointments-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into appointments          ( dept_name,                                       create_time )           values ( ?,                                       ? )\r\n### Cause: java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value\n; Field \'appointment_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value','2025-07-05 10:52:57',65),(149,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-05 16:21:35\",\"date\":\"2025-07-05\",\"deptName\":\"眼科\",\"doctorName\":\"里斯\",\"idCard\":\"130133201002081526\",\"params\":{},\"patientName\":\"天才少年\",\"timeSlot\":\"上午\"}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\AppointmentsMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.AppointmentsMapper.insertAppointments-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into appointments          ( patient_name,             id_card,             date,             doctor_name,                          dept_name,             time_slot,                          create_time )           values ( ?,             ?,             date,             ?,                          ?,             ?,                          ? )\r\n### Cause: java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value\n; Field \'appointment_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value','2025-07-05 16:21:35',68),(150,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-05 16:24:49\",\"date\":\"2025-07-05\",\"deptName\":\"眼科\",\"doctorName\":\"里斯\",\"idCard\":\"130133201002081526\",\"params\":{},\"patientName\":\"天才少年\",\"timeSlot\":\"上午\"}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\AppointmentsMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.AppointmentsMapper.insertAppointments-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into appointments          ( patient_name,             id_card,             date,             doctor_name,                          dept_name,             time_slot,                          create_time )           values ( ?,             ?,             date,             ?,                          ?,             ?,                          ? )\r\n### Cause: java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value\n; Field \'appointment_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value','2025-07-05 16:24:49',6),(151,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-05 16:34:46\",\"date\":\"2025-07-08\",\"deptName\":\"疑难杂症科\",\"doctorName\":\"赵六\",\"idCard\":\"130123201002041254\",\"params\":{},\"patientName\":\"天才少年\",\"timeSlot\":\"上午\"}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\AppointmentsMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.AppointmentsMapper.insertAppointments-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into appointments          ( patient_name,             id_card,             date,             doctor_name,                          dept_name,             time_slot,                          create_time )           values ( ?,             ?,             date,             ?,                          ?,             ?,                          ? )\r\n### Cause: java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value\n; Field \'appointment_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'appointment_id\' doesn\'t have a default value','2025-07-05 16:34:46',19),(152,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"01ef454d-6204-4498-9ace-c132b2bc2040\",\"createTime\":\"2025-07-05 16:37:48\",\"date\":\"2025-07-08\",\"deptName\":\"疑难杂症科\",\"doctorName\":\"赵六\",\"idCard\":\"130123201002041254\",\"params\":{},\"patientName\":\"天才少年\",\"timeSlot\":\"上午\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 16:37:48',22),(153,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"e22cf1f7-001b-4a75-a6e9-784e3426b77c\",\"createTime\":\"2025-07-05 17:02:59\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"13012320100504444568\",\"params\":{},\"patientName\":\"给v他\",\"timeSlot\":\"上午\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 17:02:59',34),(154,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"1cacda5b-bde6-4915-826e-63b567c72c1e\",\"createTime\":\"2025-07-05 17:07:19\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"13012320100504444568\",\"params\":{},\"patientName\":\"是v方式\",\"timeSlot\":\"上午\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 17:07:19',23),(155,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"89f39a27-82b6-424f-9acf-3c7990b3d7eb\",\"createTime\":\"2025-07-05 17:18:41\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"13012320100504444568\",\"params\":{},\"patientName\":\"颐和园\",\"timeSlot\":\"下午\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 17:18:41',21),(156,'预约订单',2,'com.ruoyi.hospital.controller.AppointmentsController.edit()','PUT',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"1cacda5b-bde6-4915-826e-63b567c72c1e\",\"createTime\":\"2025-07-05 17:07:20\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"13012320100504444554\",\"params\":{},\"patientName\":\"是v方式\",\"timeSlot\":\"上午\",\"updateTime\":\"2025-07-05 17:59:55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 17:59:55',13),(157,'预约订单',2,'com.ruoyi.hospital.controller.AppointmentsController.edit()','PUT',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"89f39a27-82b6-424f-9acf-3c7990b3d7eb\",\"createTime\":\"2025-07-05 17:18:42\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"13012320100504442468\",\"params\":{},\"patientName\":\"颐和园\",\"timeSlot\":\"下午\",\"updateTime\":\"2025-07-05 18:00:06\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 18:00:06',9),(158,'预约订单',2,'com.ruoyi.hospital.controller.AppointmentsController.edit()','PUT',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"1cacda5b-bde6-4915-826e-63b567c72c1e\",\"createTime\":\"2025-07-05 17:07:20\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"130123201005044445\",\"params\":{},\"patientName\":\"是v方式\",\"timeSlot\":\"上午\",\"updateTime\":\"2025-07-05 18:00:52\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 18:00:52',9),(159,'预约订单',2,'com.ruoyi.hospital.controller.AppointmentsController.edit()','PUT',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"89f39a27-82b6-424f-9acf-3c7990b3d7eb\",\"createTime\":\"2025-07-05 17:18:42\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"130123201005044424\",\"params\":{},\"patientName\":\"颐和园\",\"timeSlot\":\"下午\",\"updateTime\":\"2025-07-05 18:01:01\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 18:01:01',10),(160,'预约订单',2,'com.ruoyi.hospital.controller.AppointmentsController.edit()','PUT',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"e22cf1f7-001b-4a75-a6e9-784e3426b77c\",\"createTime\":\"2025-07-05 17:03:00\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"130123201005044445\",\"params\":{},\"patientName\":\"给v他\",\"timeSlot\":\"上午\",\"updateTime\":\"2025-07-05 18:01:06\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 18:01:06',8),(161,'预约订单',2,'com.ruoyi.hospital.controller.AppointmentsController.edit()','PUT',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"e22cf1f7-001b-4a75-a6e9-784e3426b77c\",\"createTime\":\"2025-07-05 17:03:00\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"130123201005046645\",\"params\":{},\"patientName\":\"给v他\",\"timeSlot\":\"上午\",\"updateTime\":\"2025-07-05 18:01:14\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 18:01:14',8),(162,'预约订单',1,'com.ruoyi.hospital.controller.AppointmentsController.add()','POST',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"119d2b74-f0a2-4273-bf1e-ce55e38a27cf\",\"createTime\":\"2025-07-05 18:12:21\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"张三\",\"idCard\":\"130156205121325411\",\"params\":{},\"patientName\":\"给个人\",\"timeSlot\":\"夜间\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-05 18:12:21',9),(163,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/departments\",\"createTime\":\"2025-07-01 19:59:44\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2038,\"menuName\":\"部门信息\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2000,\"path\":\"departments\",\"perms\":\"\",\"routeName\":\"hospital\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 14:19:16',32),(164,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/doctors\",\"createTime\":\"2025-07-01 20:00:28\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2039,\"menuName\":\"医生信息\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2000,\"path\":\"doctors\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 14:19:22',14),(165,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/hospitals\",\"createTime\":\"2025-07-01 20:00:59\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2040,\"menuName\":\"医院信息\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2000,\"path\":\"hospitals\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 14:19:28',10),(166,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/records\",\"createTime\":\"2025-07-01 20:01:42\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2041,\"menuName\":\"记录信息\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2000,\"path\":\"records\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 14:19:33',12),(167,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/schedules\",\"createTime\":\"2025-07-01 20:02:39\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2042,\"menuName\":\"医生排班\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"schedules\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 14:19:38',10),(168,'医院信息',1,'com.ruoyi.hospital.controller.HospitalsController.add()','POST',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"陕西省西安市西安工程大学临潼校区\",\"createTime\":\"2025-07-07 14:52:34\",\"deleted\":0,\"description\":\"点赞\",\"hospitalId\":2,\"hospitalName\":\"唐都医院\",\"params\":{},\"phone\":\"15353564523\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 14:52:34',59),(169,'医院信息',1,'com.ruoyi.hospital.controller.HospitalsController.add()','POST',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"上海市黄浦区瑞金二路197号\",\"createTime\":\"2025-07-07 15:12:20\",\"description\":\"上海瑞金医院是上海交通大学医学院附属医院，以血液病、内分泌代谢病、微创手术等为特色专科。\",\"hospitalId\":3,\"hospitalLevel\":\"三级甲等\",\"hospitalName\":\"上海瑞金医院\",\"params\":{},\"phone\":\"021-64370045\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 15:12:20',17),(170,'医院信息',1,'com.ruoyi.hospital.controller.HospitalsController.add()','POST',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"广东省广州市越秀区中山二路58号\",\"createTime\":\"2025-07-07 15:12:46\",\"description\":\"广州中山医院是华南地区著名的综合性医院，以心血管病、肿瘤治疗和器官移植见长。\",\"hospitalId\":4,\"hospitalLevel\":\"三级乙等\",\"hospitalName\":\"广州中山医院\",\"params\":{},\"phone\":\"020-81332299\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 15:12:46',8),(171,'医院信息',1,'com.ruoyi.hospital.controller.HospitalsController.add()','POST',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"广东省深圳市罗湖区东门北路1017号\",\"createTime\":\"2025-07-07 15:13:16\",\"description\":\"深圳人民医院是深圳市最早的三甲医院之一，以创伤急救、肿瘤综合治疗为特色。\",\"hospitalId\":5,\"hospitalLevel\":\"二级甲等\",\"hospitalName\":\"深圳人民医院\",\"params\":{},\"phone\":\"0755-25533018\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 15:13:16',6),(172,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/departments\",\"createTime\":\"2025-07-01 19:59:44\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2038,\"menuName\":\"科室信息\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2000,\"path\":\"departments\",\"perms\":\"\",\"routeName\":\"hospital\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 15:42:05',60),(173,'科室信息',1,'com.ruoyi.hospital.controller.DepartmentsController.add()','POST',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 15:50:27\",\"deptName\":\"神经科\",\"description\":\"不太了解\",\"params\":{}}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'hospital_id\' doesn\'t have a default value\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\DepartmentsMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.DepartmentsMapper.insertDepartments-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into departments          ( dept_name,                          description,             create_time )           values ( ?,                          ?,             ? )\r\n### Cause: java.sql.SQLException: Field \'hospital_id\' doesn\'t have a default value\n; Field \'hospital_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'hospital_id\' doesn\'t have a default value','2025-07-07 15:50:27',309),(174,'科室信息',1,'com.ruoyi.hospital.controller.DepartmentsController.add()','POST',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 15:51:10\",\"deptId\":3,\"deptName\":\"神经科\",\"description\":\"不太了解\",\"hospitalId\":2,\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 15:51:10',8),(175,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/schedules/index\",\"createTime\":\"2025-07-01 20:02:39\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2042,\"menuName\":\"医生排班\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"schedules\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 16:01:13',13),(176,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospitals/schedules\",\"createTime\":\"2025-07-01 20:02:39\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2042,\"menuName\":\"医生排班\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"schedules\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 16:02:57',12),(177,'菜单管理',2,'com.ruoyi.web.controller.system.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"system/hospital/schedules\",\"createTime\":\"2025-07-01 20:02:39\",\"icon\":\"code\",\"isCache\":\"1\",\"isFrame\":\"1\",\"menuId\":2042,\"menuName\":\"医生排班\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"schedules\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 16:03:59',7),(178,'代码生成',2,'com.ruoyi.generator.controller.GenController.editSave()','PUT',1,'admin','研发部门','/tool/gen','127.0.0.1','内网IP','{\"businessName\":\"departments\",\"className\":\"Departments\",\"columns\":[{\"capJavaField\":\"DeptId\",\"columnId\":20,\"columnName\":\"dept_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-07-01 14:36:21\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":true,\"insert\":true,\"isIncrement\":\"1\",\"isInsert\":\"1\",\"isPk\":\"1\",\"isRequired\":\"0\",\"javaField\":\"deptId\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"DeptName\",\"columnComment\":\"科室名称\",\"columnId\":21,\"columnName\":\"dept_name\",\"columnType\":\"varchar(50)\",\"createBy\":\"admin\",\"createTime\":\"2025-07-01 14:36:21\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"deptName\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"HospitalId\",\"columnComment\":\"所属医院ID\",\"columnId\":22,\"columnName\":\"hospital_id\",\"columnType\":\"int\",\"createBy\":\"admin\",\"createTime\":\"2025-07-01 14:36:21\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"hospitalId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":3,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Description\",\"columnComment\":\"科室简介\",\"columnId\":23,\"columnName\":\"description\",\"columnType\":\"text\",\"createBy\":\"admin\",\"createTime\":\"2025-07-01 14:36:21\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"editor\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"0\",\"javaField\":\"des','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 16:06:13',155),(179,'科室信息',1,'com.ruoyi.hospital.controller.DepartmentsController.add()','POST',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 16:09:31\",\"deptId\":4,\"deptName\":\"肾脏科\",\"description\":\"老铁们快冲\",\"hospitalId\":3,\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 16:09:31',9),(180,'科室信息',1,'com.ruoyi.hospital.controller.DepartmentsController.add()','POST',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 16:21:34\",\"deptId\":5,\"deptName\":\"肺部科室\",\"description\":\"<strong>少吸烟﻿</strong>\",\"hospitalId\":5,\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 16:21:34',43),(181,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 16:21:35\",\"deleted\":0,\"deptId\":5,\"deptName\":\"肺部科室\",\"description\":\"少吸烟﻿\",\"hospitalId\":5,\"params\":{},\"updateTime\":\"2025-07-07 17:21:58\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:21:58',19),(182,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-03 14:25:07\",\"deleted\":0,\"deptId\":1,\"deptName\":\"眼科\",\"description\":\"专业诊治各类眼科疾病\",\"hospitalId\":1,\"params\":{},\"updateTime\":\"2025-07-07 17:22:52\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:22:52',6),(183,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-04 17:34:47\",\"deleted\":0,\"deptId\":2,\"deptName\":\"疑难杂症科\",\"description\":\"专攻各类疑难病症诊断与治疗\",\"hospitalId\":1,\"params\":{},\"updateTime\":\"2025-07-07 17:23:01\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:23:01',9),(184,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 15:51:10\",\"deleted\":0,\"deptId\":3,\"deptName\":\"神经科\",\"description\":\"神经系统疾病专业诊疗中心\",\"hospitalId\":2,\"params\":{},\"updateTime\":\"2025-07-07 17:23:08\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:23:08',6),(185,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 16:09:31\",\"deleted\":0,\"deptId\":4,\"deptName\":\"肾脏科\",\"description\":\"肾脏疾病诊疗与透析治疗\",\"hospitalId\":3,\"params\":{},\"updateTime\":\"2025-07-07 17:23:14\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:23:14',7),(186,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 16:21:35\",\"deleted\":0,\"deptId\":5,\"deptName\":\"肺部科室\",\"description\":\"呼吸道疾病预防与治疗，倡导健康生活\",\"hospitalId\":5,\"params\":{},\"updateTime\":\"2025-07-07 17:23:24\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:23:24',9),(187,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 16:21:35\",\"deleted\":0,\"deptId\":5,\"deptName\":\"呼吸内科\",\"description\":\"呼吸道疾病预防与治疗，倡导健康生活\",\"hospitalId\":5,\"params\":{},\"updateTime\":\"2025-07-07 17:23:36\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:23:36',6),(188,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 16:09:31\",\"deleted\":0,\"deptId\":4,\"deptName\":\"肾脏内科\",\"description\":\"肾脏疾病诊疗与透析治疗\",\"hospitalId\":3,\"params\":{},\"updateTime\":\"2025-07-07 17:23:45\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:23:45',5),(189,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 15:51:10\",\"deleted\":0,\"deptId\":3,\"deptName\":\"神经内科\\t\",\"description\":\"神经系统疾病专业诊疗中心\",\"hospitalId\":2,\"params\":{},\"updateTime\":\"2025-07-07 17:23:52\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:23:52',8),(190,'科室信息',2,'com.ruoyi.hospital.controller.DepartmentsController.edit()','PUT',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-04 17:34:47\",\"deleted\":0,\"deptId\":2,\"deptName\":\"疑难病科\",\"description\":\"专攻各类疑难病症诊断与治疗\",\"hospitalId\":1,\"params\":{},\"updateTime\":\"2025-07-07 17:24:00\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 17:24:00',6),(191,'医生排班',1,'com.ruoyi.hospital.controller.SchedulesController.add()','POST',1,'admin','研发部门','/system/schedules','127.0.0.1','内网IP','{\"afternoonEnd\":\"17:03\",\"afternoonRemaining\":2,\"afternoonStart\":\"14:02\",\"createTime\":\"2025-07-07 20:03:49\",\"date\":\"2025-07-09\",\"doctorId\":4,\"doctorName\":\"赵六\",\"morningEnd\":\"11:03\",\"morningRemaining\":2,\"morningStart\":\"07:03\",\"nightEnd\":\"23:03\",\"nightRemaining\":2,\"nightStart\":\"20:03\",\"params\":{},\"scheduleId\":4}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:03:49',29),(192,'预约订单',3,'com.ruoyi.hospital.controller.AppointmentsController.remove()','DELETE',1,'admin','研发部门','/system/appointments/e22cf1f7-001b-4a75-a6e9-784e3426b77c','127.0.0.1','内网IP','[\"e22cf1f7-001b-4a75-a6e9-784e3426b77c\"]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:09:23',16),(193,'预约订单',3,'com.ruoyi.hospital.controller.AppointmentsController.remove()','DELETE',1,'admin','研发部门','/system/appointments/89f39a27-82b6-424f-9acf-3c7990b3d7eb','127.0.0.1','内网IP','[\"89f39a27-82b6-424f-9acf-3c7990b3d7eb\"]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:10:52',15),(194,'预约订单',3,'com.ruoyi.hospital.controller.AppointmentsController.remove()','DELETE',1,'admin','研发部门','/system/appointments/1cacda5b-bde6-4915-826e-63b567c72c1e','127.0.0.1','内网IP','[\"1cacda5b-bde6-4915-826e-63b567c72c1e\"]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:10:55',8),(195,'科室信息',1,'com.ruoyi.hospital.controller.DepartmentsController.add()','POST',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 20:12:32\",\"deptId\":6,\"deptName\":\"儿科\",\"description\":\"专注于儿童疾病预防与治疗，提供温馨就诊环境，医护团队经验丰富。\",\"hospitalId\":3,\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:12:32',11),(196,'医院信息',1,'com.ruoyi.hospital.controller.HospitalsController.add()','POST',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"上海\",\"createTime\":\"2025-07-07 20:13:32\",\"description\":\"专注人民健康\",\"hospitalId\":6,\"hospitalLevel\":\"三级甲等\",\"hospitalName\":\"复旦大学附属妇产科医院\",\"params\":{},\"phone\":\"0212-21558\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:13:32',9),(197,'科室信息',1,'com.ruoyi.hospital.controller.DepartmentsController.add()','POST',1,'admin','研发部门','/system/departments','127.0.0.1','内网IP','{\"createTime\":\"2025-07-07 20:13:49\",\"deptId\":7,\"deptName\":\"妇产科\",\"description\":\"提供孕产全程管理服务，设有VIP产房，技术力量雄厚。\",\"hospitalId\":6,\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:13:49',10),(198,'医生信息',2,'com.ruoyi.hospital.controller.DoctorsController.edit()','PUT',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":50,\"createTime\":\"2025-07-04 17:45:07\",\"deleted\":0,\"deptId\":2,\"doctorId\":6,\"doctorName\":\"王建国\",\"introduction\":\"北京协和医院心血管内科专家，擅长复杂冠心病介入治疗，发表SCI论文20余篇。\",\"params\":{},\"specialty\":\"高血压、冠心病、心力衰竭\",\"title\":\"主任医师\",\"updateTime\":\"2025-07-07 20:16:16\",\"workYears\":15}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:16:16',17),(199,'医生信息',2,'com.ruoyi.hospital.controller.DoctorsController.edit()','PUT',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":30,\"createTime\":\"2025-07-04 17:39:34\",\"deleted\":0,\"deptId\":6,\"doctorId\":5,\"doctorName\":\"李雯\",\"introduction\":\"上海儿童医学中心骨干医生，曾赴美国波士顿儿童医院进修，诊疗耐心细致。\",\"params\":{},\"specialty\":\"儿童哮喘、新生儿疾病\",\"title\":\"副主任医师\",\"updateTime\":\"2025-07-07 20:17:49\",\"workYears\":10}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:17:49',9),(200,'医生信息',2,'com.ruoyi.hospital.controller.DoctorsController.edit()','PUT',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":30,\"createTime\":\"2025-07-04 17:39:34\",\"deleted\":0,\"deptId\":6,\"doctorId\":5,\"doctorName\":\"李雯\",\"introduction\":\"上海儿童医学中心骨干医生，曾赴美国波士顿儿童医院进修，诊疗耐心细致。\",\"params\":{},\"specialty\":\"儿童哮喘、新生儿疾病\",\"title\":\"副主任医师\",\"updateTime\":\"2025-07-07 20:18:03\",\"workYears\":10}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:18:03',7),(201,'医生信息',2,'com.ruoyi.hospital.controller.DoctorsController.edit()','PUT',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":50,\"createTime\":\"2025-07-04 17:45:07\",\"deleted\":0,\"deptId\":2,\"doctorId\":6,\"doctorName\":\"王建国\",\"introduction\":\"北京协和医院心血管内科专家，擅长复杂冠心病介入治疗，发表SCI论文20余篇。\",\"params\":{},\"specialty\":\"高血压、冠心病、心力衰竭\",\"title\":\"主任医师\",\"updateTime\":\"2025-07-07 20:21:14\",\"workYears\":15}',NULL,1,'\r\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'\'王建国\',\n            work_years = 15,\n            consultation_fee = 50,\n   \' at line 5\r\n### The error may exist in file [F:\\RuoYi-Vue-master\\ruoyi-hospital\\target\\classes\\mapper\\DoctorsMapper.xml]\r\n### The error may involve com.ruoyi.hospital.mapper.DoctorsMapper.updateDoctors-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update doctors          SET dept_id = ?,             title = ?,             specialty = ?,             ?,             work_years = ?,             consultation_fee = ?,             introduction = ?,             create_time = ?,             update_time = ?,             deleted = ?          where doctor_id = ?\r\n### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'\'王建国\',\n            work_years = 15,\n            consultation_fee = 50,\n   \' at line 5\n; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'\'王建国\',\n            work_years = 15,\n            consultation_fee = 50,\n   \' at line 5','2025-07-07 20:21:14',14),(202,'医生信息',2,'com.ruoyi.hospital.controller.DoctorsController.edit()','PUT',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":50,\"createTime\":\"2025-07-04 17:45:07\",\"deleted\":0,\"deptId\":2,\"doctorId\":6,\"doctorName\":\"王建国\",\"introduction\":\"北京协和医院心血管内科专家，擅长复杂冠心病介入治疗，发表SCI论文20余篇。\",\"params\":{},\"specialty\":\"高血压、冠心病、心力衰竭\",\"title\":\"主任医师\",\"updateTime\":\"2025-07-07 20:22:42\",\"workYears\":15}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:22:42',21),(203,'医生信息',2,'com.ruoyi.hospital.controller.DoctorsController.edit()','PUT',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":60,\"createTime\":\"2025-07-03 14:26:29\",\"deleted\":0,\"deptId\":1,\"doctorId\":1,\"doctorName\":\"陈芳\",\"introduction\":\"中山眼科中心博士生导师，国内首批开展飞秒激光手术的专家之一。\",\"params\":{},\"specialty\":\"白内障超声乳化、青光眼\",\"title\":\"主任医师\",\"updateTime\":\"2025-07-07 20:23:38\",\"workYears\":20}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:23:38',6),(204,'医生信息',2,'com.ruoyi.hospital.controller.DoctorsController.edit()','PUT',1,'admin','研发部门','/system/doctors','127.0.0.1','内网IP','{\"consultationFee\":40,\"createTime\":\"2025-07-04 16:22:52\",\"deleted\":0,\"deptId\":7,\"doctorId\":2,\"doctorName\":\"刘婷\",\"introduction\":\"复旦大学附属妇产科医院产科组长，擅长多胎妊娠管理，接生超3000例。\",\"params\":{},\"specialty\":\"高危妊娠、妇科肿瘤\",\"title\":\"副主任医师\",\"updateTime\":\"2025-07-07 20:24:15\",\"workYears\":12}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:24:15',7),(205,'医生信息',5,'com.ruoyi.hospital.controller.DoctorsController.export()','POST',1,'admin','研发部门','/system/doctors/export','127.0.0.1','内网IP','{\"pageSize\":\"10\",\"pageNum\":\"1\"}',NULL,0,NULL,'2025-07-07 20:44:32',943),(206,'预约订单',2,'com.ruoyi.hospital.controller.AppointmentsController.edit()','PUT',1,'admin','研发部门','/system/appointments','127.0.0.1','内网IP','{\"appointmentId\":\"119d2b74-f0a2-4273-bf1e-ce55e38a27cf\",\"createTime\":\"2025-07-05 18:12:22\",\"date\":\"2025-07-03\",\"deptName\":\"眼科\",\"doctorName\":\"陈芳\",\"hospitalName\":\"协和医院\",\"idCard\":\"130156205121325411\",\"params\":{},\"patientName\":\"给个人\",\"timeSlot\":\"夜间\",\"updateTime\":\"2025-07-07 20:51:38\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-07 20:51:38',22),(207,'医院信息',2,'com.ruoyi.hospital.controller.HospitalsController.edit()','PUT',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"北京\",\"createTime\":\"2025-07-01 20:20:23\",\"deleted\":0,\"description\":\"北京协和医院是一所集医疗、教学、科研于一体的大型综合医院，是国家卫生健康委员会指定的全国疑难重症诊治指导中心。\",\"hospitalId\":1,\"hospitalLevel\":\"三级甲等\",\"hospitalName\":\"协和医院\",\"params\":{},\"phone\":\"15533681891\",\"updateTime\":\"2025-07-08 09:25:19\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-08 09:25:19',42),(208,'医院信息',2,'com.ruoyi.hospital.controller.HospitalsController.edit()','PUT',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"北京市\",\"createTime\":\"2025-07-01 20:20:23\",\"deleted\":0,\"description\":\"北京协和医院是一所集医疗、教学、科研于一体的大型综合医院，是国家卫生健康委员会指定的全国疑难重症诊治指导中心。\",\"hospitalId\":1,\"hospitalLevel\":\"三级甲等\",\"hospitalName\":\"协和医院\",\"params\":{},\"phone\":\"15533681891\",\"updateTime\":\"2025-07-08 09:27:06\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-08 09:27:06',9),(209,'医院信息',2,'com.ruoyi.hospital.controller.HospitalsController.edit()','PUT',1,'admin','研发部门','/system/hospitals','127.0.0.1','内网IP','{\"address\":\"上海市\",\"createTime\":\"2025-07-07 20:13:32\",\"deleted\":0,\"description\":\"专注人民健康\",\"hospitalId\":6,\"hospitalLevel\":\"三级甲等\",\"hospitalName\":\"复旦大学附属妇产科医院\",\"params\":{},\"phone\":\"0212-21558\",\"updateTime\":\"2025-07-08 09:27:15\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-07-08 09:27:15',8);

/*Table structure for table `sys_post` */

DROP TABLE IF EXISTS `sys_post`;

CREATE TABLE `sys_post` (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='岗位信息表';

/*Data for the table `sys_post` */

insert  into `sys_post`(`post_id`,`post_code`,`post_name`,`post_sort`,`status`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,'ceo','董事长',1,'0','admin','2025-06-24 11:49:28','',NULL,''),(2,'se','项目经理',2,'0','admin','2025-06-24 11:49:28','',NULL,''),(3,'hr','人力资源',3,'0','admin','2025-06-24 11:49:28','',NULL,''),(4,'user','普通员工',4,'0','admin','2025-06-24 11:49:28','',NULL,'');

/*Table structure for table `sys_role` */

DROP TABLE IF EXISTS `sys_role`;

CREATE TABLE `sys_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色信息表';

/*Data for the table `sys_role` */

insert  into `sys_role`(`role_id`,`role_name`,`role_key`,`role_sort`,`data_scope`,`menu_check_strictly`,`dept_check_strictly`,`status`,`del_flag`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,'超级管理员','admin',1,'1',1,1,'0','0','admin','2025-06-24 11:49:28','',NULL,'超级管理员'),(2,'普通角色','common',2,'2',1,1,'0','0','admin','2025-06-24 11:49:28','',NULL,'普通角色');

/*Table structure for table `sys_role_dept` */

DROP TABLE IF EXISTS `sys_role_dept`;

CREATE TABLE `sys_role_dept` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色和部门关联表';

/*Data for the table `sys_role_dept` */

insert  into `sys_role_dept`(`role_id`,`dept_id`) values (2,100),(2,101),(2,105);

/*Table structure for table `sys_role_menu` */

DROP TABLE IF EXISTS `sys_role_menu`;

CREATE TABLE `sys_role_menu` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色和菜单关联表';

/*Data for the table `sys_role_menu` */

insert  into `sys_role_menu`(`role_id`,`menu_id`) values (2,1),(2,2),(2,3),(2,4),(2,100),(2,101),(2,102),(2,103),(2,104),(2,105),(2,106),(2,107),(2,108),(2,109),(2,110),(2,111),(2,112),(2,113),(2,114),(2,115),(2,116),(2,117),(2,500),(2,501),(2,1000),(2,1001),(2,1002),(2,1003),(2,1004),(2,1005),(2,1006),(2,1007),(2,1008),(2,1009),(2,1010),(2,1011),(2,1012),(2,1013),(2,1014),(2,1015),(2,1016),(2,1017),(2,1018),(2,1019),(2,1020),(2,1021),(2,1022),(2,1023),(2,1024),(2,1025),(2,1026),(2,1027),(2,1028),(2,1029),(2,1030),(2,1031),(2,1032),(2,1033),(2,1034),(2,1035),(2,1036),(2,1037),(2,1038),(2,1039),(2,1040),(2,1041),(2,1042),(2,1043),(2,1044),(2,1045),(2,1046),(2,1047),(2,1048),(2,1049),(2,1050),(2,1051),(2,1052),(2,1053),(2,1054),(2,1055),(2,1056),(2,1057),(2,1058),(2,1059),(2,1060);

/*Table structure for table `sys_user` */

DROP TABLE IF EXISTS `sys_user`;

CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime DEFAULT NULL COMMENT '密码最后更新时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户信息表';

/*Data for the table `sys_user` */

insert  into `sys_user`(`user_id`,`dept_id`,`user_name`,`nick_name`,`user_type`,`email`,`phonenumber`,`sex`,`avatar`,`password`,`status`,`del_flag`,`login_ip`,`login_date`,`pwd_update_date`,`create_by`,`create_time`,`update_by`,`update_time`,`remark`) values (1,103,'admin','若依','00','<EMAIL>','15888888888','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','127.0.0.1','2025-07-08 09:24:17','2025-06-24 11:49:28','admin','2025-06-24 11:49:28','','2025-07-08 09:24:16','管理员'),(2,105,'ry','若依','00','<EMAIL>','15666666666','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','127.0.0.1','2025-06-24 11:49:28','2025-06-24 11:49:28','admin','2025-06-24 11:49:28','',NULL,'测试员');

/*Table structure for table `sys_user_post` */

DROP TABLE IF EXISTS `sys_user_post`;

CREATE TABLE `sys_user_post` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户与岗位关联表';

/*Data for the table `sys_user_post` */

insert  into `sys_user_post`(`user_id`,`post_id`) values (1,1),(2,2);

/*Table structure for table `sys_user_role` */

DROP TABLE IF EXISTS `sys_user_role`;

CREATE TABLE `sys_user_role` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户和角色关联表';

/*Data for the table `sys_user_role` */

insert  into `sys_user_role`(`user_id`,`role_id`) values (1,1),(2,2);

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
