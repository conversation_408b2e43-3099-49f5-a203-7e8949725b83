 

 

 

**达内优创信息科技有限公司****实训报告**

 

**题**    **目：**           高校就业分析平台  **专**    **业：**                **学生姓名：**                 **学**    **号：**                        **班**    **级：**                 **课设企业：**     西安达内优创信息科技有限公司  **企业指导教师：**               **实习时间：**                                                               

**答辩日期：**                

 

**基于** **Java SpringBoot** **的高****校就业分析平台**

 

 

 

**摘**  **要**

 

当前社会就业形式越来越严峻，每年有大量的毕业生涌入就业市场，毕业生 与其他求职者相比处于劣势地位，毕业生无论是在工作经验、项目经验、社会经 验等方面都非常缺乏，毕业生在毕业季要花费大量的时间和精力来竞争就业,否则就要面临毕业即失业的情况。保障应届毕业生就业一直是各个高校、政府的重点工作内容，特别是在最近几年，一年比一年多的毕业生，同时企业人才需求减少，毕业生就业面临极大的挑战。

高校作为培养人才的基础一直以来都承担着极其重要的角色，教育的兴衰对 于整个社会的持续发展有着重要的意义，通过教育为社会各界提供了各色人才， 人才的融入为行业的发展带来新的活力推动着社会不断进步，人才的出现是每个 行业赖以生存的重要依据但对于每个人才的吸纳则是学生走出校们所需要面临 的第一步，每年应届毕业生与招聘单位直接都会有着频繁的接触来进行一个双方 的选择过程。21 世纪计算机的高度使用标志人类进入了高科技的时代，科技的 飞速发展正在不断改变着人类的生活，随着互联网技术的不断发展，越来越多的 高校采用线上招聘的模式将毕业生与企业连接在一起，通过校企合作、互联网匹 配等新模式来解决毕业生就业问题，线上招聘非常的方便，就业双方通过互联网 进行各项信息的交流，极大的提高了就业的效率。

本次采用 SpringBoot 框架为某高校设计一个高校毕业生就业信息管理系 统，系统功能主要包括就业管理、创业管理、考研管理、考公管理、宣讲会管理、 招聘管理和公告管理等等，不仅提高毕业生就业率，还为某高校提供了个直观的 就业数据统计的管理平台。

**关键词：就业信息，高校，SpringB****oot** **框架，管理系统**

**University Employment Analysis Platform based** **on Java SpringBoot**

 

 

**Abstract**

 

At present, the form of social employment is becoming more and more severe. Every year, a large number of graduates pour into the employment market. Compared with other job seekers, graduates are at a disadvantage. Graduates are very lack of work experience, project experience, social experience, etc. Graduates spend a lot of time and energy to compete for employment in the Season of graduation. Ensuring the employment of fresh graduates has always been a key task for various universities and governments, especially ，There are more and more graduates year by year, and at the same time, the demand for talents in enterprises is decreasing, and the employment of graduates is facing great challenges

Universities,  as  the  foundation  for  cultivating  talents,  have  always  played  an  extremely important  role.  The  rise  and  fall  of  education  is  of  great  significance  for  the   sustainable development of the entire society. Through education, various talents are provided to all sectors of society, and the integration of talents brings new vitality to the development of the industry, promoting continuous social progress, The emergence of talents is an important basis for the survival of each industry, but the absorption of each talent is the first step that students need to face when leaving school. Every year, fresh graduates and recruitment units have frequent contact to conduct a process of mutual selection. The high use of computers in the 21st century marks the beginning of a high-tech era for humanity. The rapid development of technology is constantly changing human life. With the continuous development of internet technology, more and more universities are adopting online recruitment models to connect graduates and enterprises, and solving the employment problem of graduates through new models  such as  school  enterprise cooperation  and  internet  matching.  Online  recruitment  is  very  convenient,  The  exchange  of various information between both employment parties through the Internet has greatly improved the efficiency of employment.

This  time,  the  SpringBoot  framework  is  used  to  design  a  college  graduate  employment information management system for a certain university. The system functions mainly include employment  management,   entrepreneurship  management,  postgraduate   entrance  examination management,   public   entrance   examination   management,   lecture   management,   recruitment management, and announcement management, etc. This not only improves the employment rate of graduates, but also provides an intuitive management platform for employment data statistics for a certain university.

**Keywords:** **employment information, universities,** **SpringBoot framework, management** **systems**

[目    录](#bookmark1)[**第一章** **绪论**   **6**](#bookmark3)[1.1 系统开发背景  6](#bookmark5)[1.2 系统开发意义  8](#bookmark7)[1.3 国内外研究现状  9](#bookmark9)[**第二章** **可行性分析**  **11**](#bookmark11)[2.1 社会可行性   11](#bookmark13)[2.2 技术可行性   11](#bookmark15)[2.3 操作可行性   11](#bookmark17)[2.4 系统的技术介绍   11](#bookmark19)[2.5 系统开发平台及运行环境   13](#bookmark31)[2.5.1 系统开发平台  13](#bookmark33)[2.5.2 运行环境  13](#bookmark35)[**第三章** **需求分析**   **14**](#bookmark37)[3.1 系统功能模块概述和分析   14](#bookmark39)[3.1.1 登录   14](#bookmark41)[3.1.2 查询学生信息   14](#bookmark43)[3.1.3 添加就业信息   15](#bookmark47)[3.1.4编辑学生信息   16](#bookmark57)[3.1.5 查询图表统计信息   16](#bookmark59)[3.2 系统功能模块设计   17](#bookmark61)[3.3 流程分析   17](#bookmark63)[3.4 用例分析   18](#bookmark65)[3.5 数据库分析   19](#bookmark67)[3.6 设计类图分析   22](#bookmark69)[3.7 交互类图分析   23](#bookmark71)[3.7.1 顺序图   23](#bookmark73)[3.8 系统架构设计图   24](#bookmark75)[**第四章** **高校毕业生就业信息管理系统的设计与****实现**   **25**](#bookmark77)[4.1 登录页面设计   25](#bookmark79)[4.2 系统首页界面   25](#bookmark81)

[4.3  用户管理页面  26](#bookmark83)[4.4  学院管理页面  28](#bookmark85)[4.6  就业管理页面  29](#bookmark89)[4.8  接口设计表  32](#bookmark93)[**第五章** **总结**   **35**](#bookmark95)[**致谢**   **36**](#bookmark97)[**参考文献**   **37**](#bookmark99)

 

**第一章** **绪论**

 

**1.1**  **系统开发背景**

 

当前社会就业形式越来越严峻，每年有大量的毕业生涌入就业市场，毕业生 与其他求职者相比处于劣势地位，毕业生无论是在工作经验、项目经验、社会经 验等方面都非常缺乏，毕业生在毕业季要花费大量的时间和精力来竞争就业。保 障应届毕业生就业一直是各个高校、政府的重点工作内容，特别是在疫情时期， 企业人才需求减少、很多线下招聘会取消， 毕业生就业面临极大的挑战。高校作 为培养人才的基础一直以来都承担着极其重要的角色，教育的兴衰对于整个社会 的持续发展有着重要的意义，通过教育为社会各界提供了各色人才，人才的融入 为行业的发展带来新的活力推动着社会不断进步，人才的出现是每个行业赖以生 存的重要依据但对于每个人才的吸纳则是学生走出校们所需要面临的第一步，每 年应届毕业生与招聘单位直接都会有着频繁的接触来进行一个双方的选择过程。 陈莉在《经济新常态背景下高校毕业生就业促进机制的构建及实践》中指出， 在 现代社会不断持续发展过程中,就业问题一直以来都是十分重要的社会性问题, 同时也是促进社会经济发展及社会和谐的重要任务,尤其在经济新常态背景下, 受新冠疫情因素影响,高校毕业生就业为题已成为社会焦点问题,亟待解决。为实 现就业问题的有效解决,政府部门、就业单位及高校需要注意构建就业促进机制, 并积极有效落实,从而为高校毕业生就业问题的解决提供更好基础及保证,使大 学生的就业需求得到满足。对于高校来说，如何为毕业生搭建高效信息管理平台， 促进毕业生快速选择就业是目前高校急需要解决的问题。

新世纪通过对计算机的使用的普及程度越来越深，代表着人类社会进入了高 速发展的科技时代，科技的飞速发展提升了各项基础设施的建设程度从而不断改 变着人类的生活状况，飞速发展的科技带动了各种技术的不断进步，随着互联网 技术的不断发展人们在此基础上融合了计算机技术、语言开发技术提出更为高效 可靠的信息技术的概念，在这日益网络化的时代信息技术成为了各个行业赖以生 存的重要保障，长期的发展人们对于信息技术的使用率越来越高，信息技术的不 断参与对于行业的发展起到了良好的推动作用，信息技术以其对数据处理的高效 性、信息保障的高安全性而备受行业的青睐， 如今信息技术以其强大的数据处理 能力保持着高度的影响力，信息技术的蓬勃发展也变相的推动的行业的发展，信 息技术的强大能力与广阔的发展空间让人们有目共睹，各个行业都开始利用信息 技术来为自己提供便利的服务，信息技术与行业生产的不断融合不仅提高了行业

的生产管理效率也给行业带来了巨额的经济利润。随着信息化技术在各个行业发 挥的作用越来越重要，社会各界开始大力推行信息化建设，信息技术开始融入到 各行各业并与之息息相关相辅相成，随着国家对于信息化建设的重视程度不断提 高，近年来信息技术与各个领域开始了频繁碰撞，在改革开发以来呈现了大踏步 式的前进，信息技术的不断提升带动了社会经济的不断进步，各行各业开始陆续 利用信息技术来对行业发展进行相关管理，信息技术的运用开始在行业中发挥出 重要作用。

在信息化发展的大背景下教育界也正进行着大刀阔斧的改革创新，教育信息 化、管理信息化、校园信息化也将成为教育发展的最终趋势。采用热门的网络通 讯技术、计算机技术以及软件开发技术想结合的方式来为教育提供先进高效的服 务必将在不久的将来成为教育改革的重点工程。这都是信息技术技术与教育结合 的必然产物也将会推动校园信息化建设更有效的往前迈进。

本次采用 SpringBoot 框架为某高校设计一个高校毕业生就业信息管理系 统，系统功能主要包括就业管理、创业管理、考研管理、考公管理、宣讲会管理、 招聘管理和公告管理等等，不仅提高毕业生就业率，还为某高校提供了个直观的 就业数据统计的管理平台。

**1.2**  **系统开发意义**

 

随着社会各界对于信息技术利用程度的不断提升，信息技术开始被广泛应用 于各个行业，信息技术的高度利用不仅提高了行业的管理效率也为行业带来了可 观的经济效益。利用信息技术来参与行业的管理已成为各行各业维持长久发展的 必然趋势。

高校作为社会各类人才的培养基地一直以来都在为社会的发展前进承担着 不可替代的作用，对于社会人才输送率的高低也成为了评判高校教育成功与否的 标准。这些年对于高校毕业生的就业管理也成为学校重要工作之一，学校可以以 此为依据来对学校的教育进行评价，并根据相应的结果对于往后的教育方向做出 及时有效的调整，然后随着近年来不断增长的学生基数高校对于毕业生的就业管 理情况也开始变的困难，针对目前管理中出现的种种难题开发设计了一个基于 SpringBoot 框架的高校毕业生就业信息管理系统，主要用于改善以往的管理方 式系统的投入使用有着几点重要的意义：

首先系统的投入使用提高了毕业生就业管理的效率，这些年来随着教育的普 及程度提高，高校生源不断增加导致高校对于毕业生的就业情况变的难以管理起 来，传统的手工管理对于学生信息录入时间长、过程容易出错、查询困难等， 而 系统的投入使用可以将以前的难点变为自己的有点，不仅提高了高校自身的管理 效率也为同学们提高了便利的服务。

其次通过就业管理网站的建立实现了对于应届毕业生的规范化管理，让每个 来进行招聘的企业在校园的监管下进行，这样既可以保障应届毕业生的自身利益 也可以对前来招聘的企业进行规范化的管理，在开始时就为学生们规避了各种可 能遇到的风险，让学生们可以在一个放心的环境中与企业进行相关的沟通。

再者为高校提供了判断依据，校方通过就业管理系统的投入使用将招聘企 业、学校与学生三方整合到了一个平台之上，学校为用人单位与在校大学生之间 构建了一个桥梁，也让校方可以对毕业生去留有了一个全面了解，通过对学生就 业情况的统计分析可以作为学校教育方针的改革参考，对于高校的教育发展方向 的更改调整有着重要意义。

最后弥补了高校信息化建设的空白，今年来随着社会各界信息建设的不断进 行，高校信息化也参与了进来各类信息产品更是相继出现，教育管理类的、生活 管理类的、活动管理类的都开始服务于我们校园的日常生活， 高校信息化建设的 不断进行各种应用系统的不断加入正在渐渐弥补高校信息化建设的各种空白，为 建设全面信息化的高校贡献了一份力量。

**1.3**   **国内外研究现状**

 

当今世界信息技术的迅猛发展为各行各业的发展带来了全新的动力，行业发 展与信息技术相互融合成为了必然局势，信息技术的持续发展带领人类社会进去 了一个信息时代，如今行业的发展壮大已离不开信息化的支持，信息技术的提升 已成为了推动社会发展前进的核心力量。关于信息技术的发展要追溯到上个世 纪，计算机作为人类先进的科技发明自出现起就对人类社会的生产活动起到了极 大的改革，随着它的飞速发展所应用的领域也越来越广泛，从最初的科研领域慢 慢融入到社会的各个领域，成为了各个行业从事生产活动所必不可少的工具，起 初人们只通过计算机来进行一些简单的数字化处理，然而随着计算机于互联网结 合后数字信息技术开始呈现暴增式发展。长期的发展过程中人们对于计算机的利 用在模式上开始有所转变，依托于网络通讯技术、软件开发技术人们以计算机为 媒介实现了跨区域、跨空间的管理方式， 人们对于行业的生产管理不在受时间与 空间的限制大大提高了管理效率、降低人工成本。

纵观全球对于信息技术的利用发达一直走在前沿的位置，经历长期的发展 各项技术已经慢慢成熟，在各行各业都有了成熟产品各种 APP、网站比比皆是， 在为人们提供便利服务的同时也获得了可观的经济利益，发达国家的信息产品在 这长久的使用过程中不断经历着各种变革创新，通用使用经历的不断反馈调节慢 慢弥补了各种信息化建设的空白，如今信息技术的高度运用已成为行业实现信息 化建设的主要手段，相较于国外的信息化建设成果我们信息化建设目前还处于建 设阶段，与发达国家比存在着不小的差距，虽然我们一直在努力追赶但从宏观角 度来讲还存在着诸多不足：

（1）人才方面的缺失：信息化的建设是一个漫长的过程需要各方面的不断 投入，技术人员作为信息建设的基石是每个行业发展信息化建设的必备力量，行 业的发展需要人才的不断参与努力才能使信息化的建设最大限度的发挥出来，但 受限于国内信息技术起步晚的原因我们的信息化人才有所不足，特别是在一些高 端的技术领域更是缺失的厉害。

（2）配套设施的健全程度：信息化建设持续发展需要各项基础设施的支持， 漫长的信息化建设过程中随着信息技术的不断提升对于硬件质量要求的也开始 有所上升，目前国内在基础设施特别是一些核心硬件的制造上还存在有众多短 板，只有弥补了这些短板才能对信息化建设做出更好的支持。

（3）软件的运用的成熟度不高：信息化建设的全面发展需要各个领域的平 衡进行，需要在各领域的生产活动有各类软件的不断支持，就这对于各类软件的 使用要求提出不同的挑战，然而纵观国内各个行业对于软件的运用的程度还不算

太高，很多都只是停留在简单的数据出理层次，对于信息技术的利用率有待深层 次的开发。

（4）缺乏危机意识：在这技术革新日益加快的年代各种软件技术的都可能 在短期被淘汰，然而国内多数行业仍只是满足于现有的信息建设程度，就像比尔 盖茨说的：我们离产品的淘汰只有 18 个月，可见信息技术类产品更新速度之快， 所以我们时刻都需要保持一定的危机意识，对于技术创新不能止步。

就目前的信息化建设情况来看对于信息技术的建设我们虽然有所落后，但经 过多年的发展差距正在被我们慢慢追赶，随着我国信息技术的力度不断加大我国 的信息化建设必将达到一个前所未有的高度。

**第二章** **可行性分析**

 

**2.1**  **社会可行性**

 

随着计算机技术的发展，越来越多的高校开始使用管理系统来替代传统的手 工管理方式。高校使用管理系统能够提供管理的效率同时也能直观的看到自己想 要的数据。《高校毕业生就业信息管理系统》系统主要目的是进行毕业生就业信 息统计，并且严格按照国家法律法规来进行研究和实践，并无法律和政策方面的 限制。

**2.2**  **技术可行性**

 

本次介绍的系统采用的是 Thymeleaf+MySQL+SpringBoot 框架进行开发， Windows10 操作系统，前端采用Bootstrap 框架，采用 MyBatis 与后台数据库进 行连接，MyBatis 是对 Jdbc 的封装，完成数据的添加、修改、删除、查询等功 能。SpringBoot 框架整合 Web 项目框架，功能强大而且稳定，而 MySQL 灵活易 维护在开发方面具有方便快捷、使用灵活的特点， 以及目前的广泛实际应用，因 此使用 Thymeleaf、SpringBoot、MySQL 来完成该系统整体开发，从而说明本系 统在技术方面可行。

硬件方面，科技飞速发展的今天，硬件更新的速度越来越快，容量越来越大， 可靠性越来越高，价格越来越低，其硬件平台完全能满足此系统的需要。

**2.3**  **操作可行性**

 

目前，大多数计算机都能运行该系统，该系统的安装、调试、运行不会改变 原计算机系统的设置和布局，并且系统界面简单，提示信息完整，由相关人员指 导便能够方便的操作此软件。

**2.4**  **系统的技术介绍**

 

在整个Vue.js项目中，包括创建毕业信息提交页面和相关功能的实现，我们小组使用了一系列的技术和工具。这些技术的组合确保了前端界面的交互性、数据处理的有效性以及后端的稳定运行。以下是所用到的主要技术列表：
2.4.1前端技术
1.  Vue.js - 用于构建用户界面的渐进式JavaScript框架。
2. Element UI - Vue.js的桌面端组件库，用于快速构建高质量的用户界面。
3. Vuex - Vue.js的状态管理模式，用于管理组件状态，使得数据和状态的管理更为集中和规范。
4. Vue Router - Vue.js的官方路由管理器，用于构建单页面应用（SPA）。
5. Axios - 用于浏览器和node.js的基于Promise的HTTP客户端，处理HTTP请求。
6. Element Upload - Element UI提供的上传组件，用于实现文件上传功能。
7. SCSS/CSS - 用于样式的定义和布局。
**2.4.2**后端技术
1. Spring Boot - （假设使用）用于简化新Spring应用的初始搭建以及开发过程的框架。
2. MyBatis - （假设使用）持久层框架，用于操作数据库，将SQL语句与程序代码分离。
3. MySQL - 关系型数据库管理系统，用于存储和管理用户数据和应用数据。
 数据库
1. MySQL - 用于数据存储，处理数据的创建、读取、更新和删除（CRUD）操作。

**2.5**  **系统开发平台及运行环境**

 

**2.5.1**  **系统开发平台**

 

 开发工具和环境
1. Node.js - JavaScript运行环境，常用于运行前端开发工具。
2. Webpack - 前端资源加载/打包工具，Vue CLI项目中默认使用Webpack进行资源的编译打包。
3. Git - 版本控制系统，用于代码的版本管理和团队协作。
4. Visual Studio Code - 编辑器，用于编写代码，支持多种语言和框架，提供插件支持。
5. Postman - API开发工具，用于测试和调试API接口。
 其他技术和库
1. JavaScript (ES6+) - 现代JavaScript语言标准，提供了更多的语言特性支持。
2. HTML5 - 标记语言，用于构建和结构化网页内容。
这些技术的组合为构建复杂的Web应用提供了坚实的基础，使得应用不仅具备良好的用户体验，还能高效地处理数据和逻辑。

**2.5.2**  **运行环境**

 

操作系统：Windows 10。

服务器软件：Tomcat9.0 (SpringBoot 框架自带 Tomcat)。

浏览器：IE 、Fire Fox 、Edge。

 

**第三章** **需求分析**

 

**3.1**  **系统功能模块概述和分析**

 

《高校毕业生就业信息管理系统》采用 B/S 架构，主要针对学生、辅导员和管理员三类 角色。学生角色用户的功能：登录、浏览辅导员信息和管理员信息、浏览学院信息、浏览宣 讲会信息和企业招聘信息、管理个人的就业考研考公创业信息、浏览公告信息、修改个人信 息等等功能。辅导员角色用户有登录、管理学生信息的功能、管理宣讲会信息和企业招聘信 息、管理所有学生就业考研考公创业信息、管理公告信息等等功能。管理员角色用户除了有 前面所有功能外，还有管理辅导员信息的功能等等。本系统在系统的设计与开发过程中严格 遵守软件工程的规范，运用软件设计模式，从而减少系统模块间的偶合，力求做到系统的稳 定性、可重用性和可扩充性。

《高校毕业生就业信息管理系统》主要功能如下：

① 用户管理：管理员能管理学生列表、辅导员列表的信息，学生和辅导员能浏览用户 信息。

② 就业管理：管理员和辅导员可以管理所有学生的就业信息、创业信息、考公信息和 考研信息。

③ 招聘管理：管理员和辅导员可以管理宣讲会信息和企业招聘信息。

④ 公告管理：管理员和辅导员可以管理公告信息。

**3.1.1** **登录**

 

| 功能描述 | 系统用户（学生和管理员）能对系统进行登录操作                 |
| -------- | ------------------------------------------------------------ |
| 输入项   | 学号/学工号、密码                                            |
| 处理描述 | 系统用户（学生和管理员）正确输入学号/学工号、密码后，点击登录按钮，能进入登录后的页面 |
| 输出项   | 无                                                           |
| 业务规则 | 系统用户（学生和管理员）都能进行登录功能操作                 |
| 界面要求 | 友好、美观、易用                                             |

**3.1.2** **查询学生信息**

 

| 功能描述 | 系统用户（学生和管理员）能进行查看学生的操作                 |
| -------- | ------------------------------------------------------------ |
| 输入项   | 无                                                           |
| 处理描述 | 系统用户（学生和管理员）要先登录系统，登录后可以在学生列表浏览学生信息，同时也能输入学号/学工号进行搜索学生信息的操作。 |
| 输出项   | 无                                                           |
| 业务规则 | 学生只能看到自己的信息、管理员能看到所有学生信息。           |
| 界面要求 | 友好、美观、易用                                             |

**3.1.****3** **添加就业信息**

 

| 功能描述 | 系统用户（学生和管理员）能进行添加就业信息的操作             |
| -------- | ------------------------------------------------------------ |
| 输入项   | 学号/学工号、就业公司、就业状态                              |
| 处理描述 | 系统用户（学生和管理员）要先登录系统，在就业列表页面，点击添加按钮，按要求输入就业信息后，点击保存按钮，完成添加就业信息的操作。 |
| 输出项   | 无                                                           |
| 业务规则 | 管理员能进行添加所有学生就业信息的操作，学生只能进行添加自己的就业信息操作。 |
| 界面要求 | 友好、美观、易用                                             |

**3.1.****4** **编辑学生信息**

 

| 功能描述 | 管理员能进行编辑学生信息的操作                               |
| -------- | ------------------------------------------------------------ |
| 输入项   | 学号/学工号、学生名称、学生头像、毕业年份、所属学院编号      |
| 处理描述 | 管理员和辅导员要先登录系统，在用户列表页面，选择一条学生数据后，点击 编辑按钮，编辑完学生信息后，点击保存按钮，完成编辑学生信息的操作。 |
| 输出项   | 无                                                           |
| 业务规则 | 管理员能进行编辑学生信息的操作                               |
| 界面要求 | 友好、美观、易用                                             |

**3.1.****5** **查询图表统计信息**

 

| 功能描述 | 系统用户（学生和管理员）能进行查询图表统计信息的操作         |
| -------- | ------------------------------------------------------------ |
| 输入项   | 无                                                           |
| 处理描述 | 系统用户（学生和管理员）登录系统后，在首页能看到学校近五年就业率和近五年就业数的统计图表 |
| 输出项   | 无                                                           |
| 业务规则 | 系统用户（学生和管理员）能进行查询图表统计信息的操作         |
| 界面要求 | 友好、美观、易用                                             |

 

**3.2**  **系统功能模块设计**

 

根据系统功能分析，将整个系统的功能模块规划为如下的功能模块图。

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps1.png) 

 

 

**3.3**  **流程分析**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps2.png)

**3.4**  **用例分析**

 

**、**![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps3.jpg)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps4.png) 

 

 

**3.5**  **数据库分析**

 

信息系统的主要任务是通过大量数据获得管理所需要的信息，这就要求系统本身能够存 储和管理大量的数据，而这一功能的实现必须借助大型数据库系统。本系统的开发选择 MySQL 作为后台数据库开发工具。

**1.**    **概念模型设计**

概念模型用于信息世界的建模，与具体的 DBMS 无关。为了把现实世界中的具体事物 抽象、组织为某一 DBMS 支持的数据模型。人们常常首先将现实世界抽象为信息世界，然 后再将信息世界转换为机器世界。也就是说，首先把现实世界中的客观对象抽象为某一种信 息结构，这种信息结构并不依赖于具体的计算机系统和具体的 DBMS ，而是概念级的模型， 然后再把模型转换为计算机上某一个 DBMS 支持的数据模型。实际上，概念模型是现实世 界到机器世界的一个中间层次。

信息世界中包含的基本概念有实体和联系。

(1)  实体 (entity)

客观存在并可相互区别的事物称为实体。实体可以是具体的人、事、物， 也可以是抽象 的概念或联系。例如，一个学生、一门课、一个供应商、一个部门、一本 书、一位读者等 都是实体。

(2)  联系 (relationship)

在现实世界中，事物内部以及事物之间是有联系的，这些联系在信息世界中反映为实体 内部的联系和实体之间的联系。实体内部的联系通常是组成实体的各属性之间的联系。两个 实体型之间的联系可以分为 3 类，一对一联系，(1:1)；一对多联系(1 : n)；多对多联系(m : n)。

概念模型是对信息世界建模，所以概念模型应该能够方便、准确地表示信息世界中的常 用概念。概念模型的表示方法很多，其中最为常用的是 P.P.S.Chen 于 1976 年提出的实体， 联系方法(Entity-Relationship  Approach)简记为 E-R 表示法)。该方法用 E-R 图来描述现实世 界的概念模型，称为实体-联系模型，简称 E-R 模型。

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps5.png) 

 

 

 

 

 

 

**2.**    **数据库表设计**

数据库表设计主要是把概念结构设计时设计好的基本 E-R 图转换为与选用 DBMS 产品 所支持的数据模型相符合的逻辑结构。它包括数据项、记录及记录间的联系、安全性和一致 性约束等等。导出的逻辑结构是否与概念模式一致，从功能和性能上是否满足用户的要求， 要进行模式评价。

本系统数据库表如下： 

**Major** 专业表![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps6.jpg)

**字段名称**        **数据类型**        **主键**  **是否空**  **说明**

 

Department 学院表：

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps7.jpg) 

**Student** 学生表![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps8.jpg)

**字段名称**        **数据类型**        **主键**  **是否空**  **说明**

 

 

**字段名称**        **数据类型**        **主键**  **是否空**  **说明**

 

 User 管理员表![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps9.jpg)

**3.6**  **设计类图分析**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps10.png) 

**3.7**  **交互类图分析**

 

**3.7.1** **顺序图**

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps11.png) 

 

 

 

 

 

 

**3.8**  **系统架构设计图**![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps12.png)

**第四章** **高校毕业生就业信息管理系统的设** **计与实现**

《高校毕业生就业信息管理系统》主要针对学生、辅导员和管理员三类角色。整个系统 对待所有用户均采用相同的入口。不同用户的操作选项会因为其所扮演的角色的权限而有所 区别，整个系统界面风格采用管理系统的常见风格，简约大气而且方便使用，下面就具体来 叙述整个系统的设计和实现。

 

**4.1**    **登录页面设计**

 

下图是用户登录的页面，用户登录需要输入正确的学号/学工号、密码来进行登录。表 单分别会进行后端 java 验证。验证通过则会登录到管理界面，如果验证未通过则会在页面 出现相应的错误提示。

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps13.jpg) 

 

 

 

 

 

 

 

 

 

**4.2**   **系统首页界面**

下图为系统首页界面，可以看到就业数、考研数、考公数、创业数、近五年就业数和近 五年就业率的统计：![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps14.jpg)

**4.3**    **用户管理页面**

 

如下图为用户管理里面学生列表页面，可以查看个人信息。

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps15.jpg) 

编辑后点击保存按钮，就可以修改个人信息。

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps16.jpg)再如下图是用户管理里面的管理员列表页面

 

**4.4**    **学院管理页面**

 

如下图为学院管理里面学院列表页面：

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps17.jpg) 

 

**4.6**    **就业管理页面**

 

如下图是就业管理里面的就业列表页。![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps18.jpg)

**4.8**    **接****口****设计表**![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps19.jpg)

**第五章** **总结**

 

本文阐述了高校毕业生就业管理系统的开发背景与意义，并对系统进行了全面设计，进 而实现了该系统。高校毕业生就业管理系统可以实现高校对毕业生就业信息更好管理的愿 望，具有一定的实用性。

通过开发这个系统，我较为全面的掌握了 JAVA 的基本知识和编程技巧，并在开发过程 进一步提高了我的 JAVA 开发能力，也让我积累了许多宝贵经验：系统分析的好坏将决定着 的系统开发成功与否，一份好分析设计将是成功开发主要因素。我们在着手开发之前不要急 于编程，先应有较长的时间去把分析做好，做好数据库设计工作，写出相关的开发文档等。 然后再开始编写程序代码，这样做到每段代码心底都有数，有条不紊。

总的来说，利用信息化技术来实现高校毕业生就业管理系统是一个促进高校管理的必然 趋势，本文对系统的开发只是基于个人的一个尝试性的开发，虽然取得了一定的成果，但是 由于时间的限制，仍然存在许多的不足之处，因此需要后期的不断完善。

 

**致谢**

 

在开发文档完成之际，我首先要向尊敬的老师表示最真挚的谢意。

在开发文档写作期间，为了保证我们项目开发的正常进行，学院抽调了优秀的老师指导 我们进行项目开发，并且不时地询问我们项目开发的进展情况。没有郭红亮老师们的细心指 导我的开发文档与系统就不可能顺利的完成，再次对你们表示衷心地感谢。老师认真负责的 工作态度、严谨的治学风格，使我深受启发；同时也很感谢帮助过我和我一同探讨问题的同 学们。为我们这次开发的正常开展提供了必要的基础。本次开发，就要画上一个句号了。

感谢所有关心、支持、帮助过我的良师益友。

 

**参考文献**

 

 

[1]刘元园. 新时期构建高校毕业生就业状况跟踪调查机制研究[J].中国大学生就业,2023.

[2]任大伟. 基于 J2EE 的毕业生就业信息管理系统的设计与实现[D].河北工业大学,2017.

[3]王少丽. 高校毕业生就业管理系统的设计与实现[D].西安电子科技大学,2016.

[4]庞文慧.  我国高校毕业生就业研究可视化分析[J].江苏科技信息,2022.

[5]陈雄华,陈艳.Spring 企业级应用开发详解[M].电子工业出版社,2009.

[6]杨静.  基于 JAVA WEB 中 MVC 模式的研究与应用[J].电脑知识与技术,2014.

[7]赵利庆. JAVA WEB 架构中数据库优化模式的研究与发现[D].北京邮电大学,2015.

[8]喻佳,吴丹新.基于 SpringBoot 的 Web 快速开发框架[J].电脑编程技巧与维护,2021.

[9]荣艳冬.关于 Mybatis 持久层框架的应用研究[J].信息安全与技术,2015.

[10]王瑾.基于 B/S 模式的高校毕业生就业信息管理系统设计[J]. 自动化与仪器仪表,2017.

[11]黄梯云,李一军.管理信息系统（第五版）[M].高等教育出版社,2014.