 

 

 

**达内优创信息科技有限公司****实训报告**



**题**    **目：**           RuoYi-Hospital医院管理系统  **专**    **业：**                **学生姓名：**                 **学**    **号：**                        **班**    **级：**                 **课设企业：**     西安达内优创信息科技有限公司  **企业指导教师：**               **实习时间：**

**答辩日期：**



**基于** **Java SpringBoot** **的RuoYi-Hospital医院管理系统**

 

 

 

**摘**  **要**

 

随着我国医疗卫生事业的快速发展和人民群众对医疗服务质量要求的不断提高，医院管理信息化已成为现代医院发展的必然趋势。传统的医院管理模式存在效率低下、信息孤岛、资源配置不合理等问题，难以满足现代医疗服务的需求。医院作为重要的医疗服务机构，承担着为人民群众提供优质医疗服务的重要职责，其管理水平的高低直接影响着医疗服务的质量和效率。

在信息化时代背景下，医院管理系统的建设已成为提升医院管理水平、优化医疗资源配置、改善患者就医体验的重要手段。通过信息化管理，可以实现医院各部门之间的信息共享，提高工作效率，减少人为错误，为医院的科学决策提供数据支撑。21世纪计算机技术的高度发展标志着人类进入了信息化时代，科技的飞速发展正在不断改变着医疗服务模式，随着互联网技术的不断发展，越来越多的医院采用线上预约、智能排班等新模式来优化医疗服务流程，通过信息化手段提升医疗服务质量和患者满意度。

本次采用SpringBoot框架设计开发了RuoYi-Hospital医院管理系统，系统功能主要包括医院信息管理、科室管理、医生管理、预约管理、排班管理、就诊记录管理等核心模块，不仅提高了医院的管理效率，还为医院提供了一个全面、直观的信息化管理平台，有效提升了医疗服务质量。

**关键词：医院管理，信息化，SpringBoot框架，医疗服务**

**RuoYi-Hospital Management System based on Java SpringBoot**





**Abstract**



With the rapid development of China's medical and health services and the increasing demands for medical service quality from the people, hospital management informatization has become an inevitable trend in modern hospital development. Traditional hospital management models suffer from low efficiency, information silos, and unreasonable resource allocation, making it difficult to meet the needs of modern medical services. Hospitals, as important medical service institutions, bear the important responsibility of providing quality medical services to the people, and their management level directly affects the quality and efficiency of medical services.

In the context of the information age, the construction of hospital management systems has become an important means to improve hospital management levels, optimize medical resource allocation, and improve patient medical experience. Through information management, information sharing between various hospital departments can be achieved, work efficiency can be improved, human errors can be reduced, and data support can be provided for scientific decision-making in hospitals. The high development of computer technology in the 21st century marks humanity's entry into the information age. The rapid development of technology is constantly changing medical service models. With the continuous development of internet technology, more and more hospitals are adopting new models such as online appointments and intelligent scheduling to optimize medical service processes, improving medical service quality and patient satisfaction through information technology.

This time, the SpringBoot framework was used to design and develop the RuoYi-Hospital management system. The system functions mainly include hospital information management, department management, doctor management, appointment management, scheduling management, medical record management and other core modules. This not only improves the management efficiency of hospitals, but also provides hospitals with a comprehensive and intuitive information management platform, effectively improving the quality of medical services.

**Keywords:** **hospital management, informatization, SpringBoot framework, medical services**

[目    录](#bookmark1)[**第一章** **绪论**   **6**](#bookmark3)[1.1 系统开发背景  6](#bookmark5)[1.2 系统开发意义  8](#bookmark7)[1.3 国内外研究现状  9](#bookmark9)[**第二章** **可行性分析**  **11**](#bookmark11)[2.1 社会可行性   11](#bookmark13)[2.2 技术可行性   11](#bookmark15)[2.3 操作可行性   11](#bookmark17)[2.4 系统的技术介绍   11](#bookmark19)[2.5 系统开发平台及运行环境   13](#bookmark31)[2.5.1 系统开发平台  13](#bookmark33)[2.5.2 运行环境  13](#bookmark35)[**第三章** **需求分析**   **14**](#bookmark37)[3.1 系统功能模块概述和分析   14](#bookmark39)[3.1.1 登录   14](#bookmark41)[3.1.2 查询学生信息   14](#bookmark43)[3.1.3 添加就业信息   15](#bookmark47)[3.1.4编辑学生信息   16](#bookmark57)[3.1.5 查询图表统计信息   16](#bookmark59)[3.2 系统功能模块设计   17](#bookmark61)[3.3 流程分析   17](#bookmark63)[3.4 用例分析   18](#bookmark65)[3.5 数据库分析   19](#bookmark67)[3.6 设计类图分析   22](#bookmark69)[3.7 交互类图分析   23](#bookmark71)[3.7.1 顺序图   23](#bookmark73)[3.8 系统架构设计图   24](#bookmark75)[**第四章** **高校毕业生就业信息管理系统的设计与****实现**   **25**](#bookmark77)[4.1 登录页面设计   25](#bookmark79)[4.2 系统首页界面   25](#bookmark81)

[4.3  用户管理页面  26](#bookmark83)[4.4  学院管理页面  28](#bookmark85)[4.6  就业管理页面  29](#bookmark89)[4.8  接口设计表  32](#bookmark93)[**第五章** **总结**   **35**](#bookmark95)[**致谢**   **36**](#bookmark97)[**参考文献**   **37**](#bookmark99)

 

**第一章** **绪论**

 

**1.1**  **系统开发背景**

 

随着我国医疗卫生事业的快速发展和医疗技术水平的不断提升，医院管理面临着前所未有的挑战和机遇。传统的医院管理模式已经难以适应现代医疗服务的需求，医院管理信息化建设成为提升医疗服务质量、优化资源配置、改善患者就医体验的重要途径。医院作为医疗卫生服务的重要载体，承担着为人民群众提供安全、有效、便民医疗服务的重要使命，其管理水平的高低直接关系到医疗服务的质量和效率。

在信息化时代背景下，医院管理系统的建设已成为现代医院发展的必然选择。通过信息化手段，可以实现医院内部各部门之间的信息共享和协同工作，提高医院运营效率，降低管理成本，为医院的科学决策提供有力支撑。特别是在新冠疫情期间，线上预约、远程医疗等信息化服务模式发挥了重要作用，有效缓解了医疗资源紧张的问题。

王明在《现代医院信息化管理系统的构建与实践》中指出，在现代医疗服务不断发展过程中，医院管理信息化一直以来都是十分重要的发展方向，同时也是促进医疗服务质量提升及医院可持续发展的重要任务。特别是在医疗改革深化的背景下，受人口老龄化、疾病谱变化等因素影响，医院管理信息化建设已成为医疗行业关注的焦点问题，亟待解决。为实现医院管理效率的有效提升，政府部门、医疗机构及技术服务商需要注意构建完善的信息化管理体系，并积极有效落实，从而为医院管理水平的提升提供更好基础及保证，使患者的医疗服务需求得到满足。对于医院来说，如何构建高效的信息管理平台，提升医疗服务质量和管理效率是目前医院急需要解决的问题。

新世纪通过对计算机的使用的普及程度越来越深，代表着人类社会进入了高 速发展的科技时代，科技的飞速发展提升了各项基础设施的建设程度从而不断改 变着人类的生活状况，飞速发展的科技带动了各种技术的不断进步，随着互联网 技术的不断发展人们在此基础上融合了计算机技术、语言开发技术提出更为高效 可靠的信息技术的概念，在这日益网络化的时代信息技术成为了各个行业赖以生 存的重要保障，长期的发展人们对于信息技术的使用率越来越高，信息技术的不 断参与对于行业的发展起到了良好的推动作用，信息技术以其对数据处理的高效 性、信息保障的高安全性而备受行业的青睐， 如今信息技术以其强大的数据处理 能力保持着高度的影响力，信息技术的蓬勃发展也变相的推动的行业的发展，信 息技术的强大能力与广阔的发展空间让人们有目共睹，各个行业都开始利用信息 技术来为自己提供便利的服务，信息技术与行业生产的不断融合不仅提高了行业

的生产管理效率也给行业带来了巨额的经济利润。随着信息化技术在各个行业发 挥的作用越来越重要，社会各界开始大力推行信息化建设，信息技术开始融入到 各行各业并与之息息相关相辅相成，随着国家对于信息化建设的重视程度不断提 高，近年来信息技术与各个领域开始了频繁碰撞，在改革开发以来呈现了大踏步 式的前进，信息技术的不断提升带动了社会经济的不断进步，各行各业开始陆续 利用信息技术来对行业发展进行相关管理，信息技术的运用开始在行业中发挥出 重要作用。

在信息化发展的大背景下教育界也正进行着大刀阔斧的改革创新，教育信息 化、管理信息化、校园信息化也将成为教育发展的最终趋势。采用热门的网络通 讯技术、计算机技术以及软件开发技术想结合的方式来为教育提供先进高效的服 务必将在不久的将来成为教育改革的重点工程。这都是信息技术技术与教育结合 的必然产物也将会推动校园信息化建设更有效的往前迈进。

本次采用SpringBoot框架设计开发了RuoYi-Hospital医院管理系统，系统功能主要包括医院信息管理、科室管理、医生管理、预约管理、排班管理、就诊记录管理等核心模块，不仅提高了医院的管理效率和服务质量，还为医院提供了一个全面、直观的信息化管理平台。

**1.2**  **系统开发意义**

 

随着社会各界对于信息技术利用程度的不断提升，信息技术开始被广泛应用 于各个行业，信息技术的高度利用不仅提高了行业的管理效率也为行业带来了可 观的经济效益。利用信息技术来参与行业的管理已成为各行各业维持长久发展的 必然趋势。

医院作为社会医疗卫生服务的重要机构一直以来都在为人民群众的健康事业承担着不可替代的作用，医疗服务质量的高低也成为了评判医院管理水平成功与否的重要标准。这些年对于医院信息化管理也成为医院发展的重要工作之一，医院可以以此为依据来对医院的管理进行评价，并根据相应的结果对于往后的发展方向做出及时有效的调整。随着近年来不断增长的患者数量和日益复杂的医疗需求，医院对于信息化管理的需求也开始变得迫切，针对目前管理中出现的种种难题开发设计了一个基于SpringBoot框架的RuoYi-Hospital医院管理系统，主要用于改善以往的管理方式，系统的投入使用有着几点重要的意义：

首先系统的投入使用提高了医院管理的效率，这些年来随着医疗需求的不断增长，患者数量不断增加导致医院对于日常管理变得越来越复杂，传统的手工管理对于患者信息录入时间长、过程容易出错、查询困难等问题日益突出，而系统的投入使用可以将以前的难点转化为优势，不仅提高了医院自身的管理效率也为患者提供了便利的服务。

其次通过医院管理系统的建立实现了对于医疗资源的规范化管理，让医院内部各科室、医生、设备等资源在统一平台下进行管理，这样既可以保障患者的就医权益也可以对医疗资源进行合理配置，在源头上就为患者规避了各种可能遇到的风险，让患者可以在一个安全、高效的环境中享受医疗服务。

再者为医院提供了科学决策的依据，医院管理层通过信息管理系统的投入使用将医生、患者、科室等各方整合到了一个平台之上，医院为医患双方构建了一个信息化的桥梁，也让医院管理层可以对医院运营状况有了全面了解，通过对医疗数据的统计分析可以作为医院发展战略的重要参考，对于医院的发展方向调整有着重要意义。

最后弥补了医院信息化建设的空白，近年来随着社会各界信息化建设的不断推进，医院信息化也积极参与其中，各类信息化产品相继出现，医疗管理类的、患者服务类的、设备管理类的都开始服务于医院的日常运营。医院信息化建设的不断推进，各种应用系统的不断加入正在逐渐弥补医院信息化建设的各种空白，为建设全面信息化的现代医院贡献了重要力量。

**1.3**   **国内外研究现状**

 

当今世界信息技术的迅猛发展为各行各业的发展带来了全新的动力，行业发 展与信息技术相互融合成为了必然局势，信息技术的持续发展带领人类社会进去 了一个信息时代，如今行业的发展壮大已离不开信息化的支持，信息技术的提升 已成为了推动社会发展前进的核心力量。关于信息技术的发展要追溯到上个世 纪，计算机作为人类先进的科技发明自出现起就对人类社会的生产活动起到了极 大的改革，随着它的飞速发展所应用的领域也越来越广泛，从最初的科研领域慢 慢融入到社会的各个领域，成为了各个行业从事生产活动所必不可少的工具，起 初人们只通过计算机来进行一些简单的数字化处理，然而随着计算机于互联网结 合后数字信息技术开始呈现暴增式发展。长期的发展过程中人们对于计算机的利 用在模式上开始有所转变，依托于网络通讯技术、软件开发技术人们以计算机为 媒介实现了跨区域、跨空间的管理方式， 人们对于行业的生产管理不在受时间与 空间的限制大大提高了管理效率、降低人工成本。

纵观全球对于信息技术的利用发达一直走在前沿的位置，经历长期的发展 各项技术已经慢慢成熟，在各行各业都有了成熟产品各种 APP、网站比比皆是， 在为人们提供便利服务的同时也获得了可观的经济利益，发达国家的信息产品在 这长久的使用过程中不断经历着各种变革创新，通用使用经历的不断反馈调节慢 慢弥补了各种信息化建设的空白，如今信息技术的高度运用已成为行业实现信息 化建设的主要手段，相较于国外的信息化建设成果我们信息化建设目前还处于建 设阶段，与发达国家比存在着不小的差距，虽然我们一直在努力追赶但从宏观角 度来讲还存在着诸多不足：

（1）人才方面的缺失：信息化的建设是一个漫长的过程需要各方面的不断 投入，技术人员作为信息建设的基石是每个行业发展信息化建设的必备力量，行 业的发展需要人才的不断参与努力才能使信息化的建设最大限度的发挥出来，但 受限于国内信息技术起步晚的原因我们的信息化人才有所不足，特别是在一些高 端的技术领域更是缺失的厉害。

（2）配套设施的健全程度：信息化建设持续发展需要各项基础设施的支持， 漫长的信息化建设过程中随着信息技术的不断提升对于硬件质量要求的也开始 有所上升，目前国内在基础设施特别是一些核心硬件的制造上还存在有众多短 板，只有弥补了这些短板才能对信息化建设做出更好的支持。

（3）软件的运用的成熟度不高：信息化建设的全面发展需要各个领域的平 衡进行，需要在各领域的生产活动有各类软件的不断支持，就这对于各类软件的 使用要求提出不同的挑战，然而纵观国内各个行业对于软件的运用的程度还不算

太高，很多都只是停留在简单的数据出理层次，对于信息技术的利用率有待深层 次的开发。

（4）缺乏危机意识：在这技术革新日益加快的年代各种软件技术的都可能 在短期被淘汰，然而国内多数行业仍只是满足于现有的信息建设程度，就像比尔 盖茨说的：我们离产品的淘汰只有 18 个月，可见信息技术类产品更新速度之快， 所以我们时刻都需要保持一定的危机意识，对于技术创新不能止步。

就目前的信息化建设情况来看对于信息技术的建设我们虽然有所落后，但经 过多年的发展差距正在被我们慢慢追赶，随着我国信息技术的力度不断加大我国 的信息化建设必将达到一个前所未有的高度。

**第二章** **可行性分析**

 

**2.1**  **社会可行性**

 

随着计算机技术的发展，越来越多的高校开始使用管理系统来替代传统的手 工管理方式。高校使用管理系统能够提供管理的效率同时也能直观的看到自己想 要的数据。《高校毕业生就业信息管理系统》系统主要目的是进行毕业生就业信 息统计，并且严格按照国家法律法规来进行研究和实践，并无法律和政策方面的 限制。

**2.2**  **技术可行性**

 

本次介绍的系统采用的是Vue+Element UI+MySQL+SpringBoot框架进行开发，Windows10操作系统，前端采用Vue 2.6.12和Element UI 2.15.14框架，采用MyBatis与后台数据库进行连接，MyBatis是对Jdbc的封装，完成数据的添加、修改、删除、查询等功能。SpringBoot 3.9.0框架整合Web项目框架，功能强大而且稳定，而MySQL灵活易维护在开发方面具有方便快捷、使用灵活的特点，以及目前的广泛实际应用，因此使用Vue、Element UI、SpringBoot、MySQL来完成该系统整体开发，从而说明本系统在技术方面可行。

硬件方面，科技飞速发展的今天，硬件更新的速度越来越快，容量越来越大， 可靠性越来越高，价格越来越低，其硬件平台完全能满足此系统的需要。

**2.3**  **操作可行性**

 

目前，大多数计算机都能运行该系统，该系统的安装、调试、运行不会改变 原计算机系统的设置和布局，并且系统界面简单，提示信息完整，由相关人员指 导便能够方便的操作此软件。

**2.4**  **系统的技术介绍**

 

在整个RuoYi-Hospital项目中，包括创建医院管理各功能模块和相关功能的实现，我们使用了一系列成熟的技术和工具。这些技术的组合确保了前端界面的交互性、数据处理的有效性以及后端的稳定运行。以下是所用到的主要技术列表：

**2.4.1前端技术**
1. Vue.js 2.6.12 - 用于构建用户界面的渐进式JavaScript框架。
2. Element UI 2.15.14 - Vue.js的桌面端组件库，用于快速构建高质量的用户界面。
3. Vuex 3.6.0 - Vue.js的状态管理模式，用于管理组件状态，使得数据和状态的管理更为集中和规范。
4. Vue Router 3.4.9 - Vue.js的官方路由管理器，用于构建单页面应用（SPA）。
5. Axios 0.28.1 - 用于浏览器和node.js的基于Promise的HTTP客户端，处理HTTP请求。
6. ECharts 5.4.0 - 用于数据可视化的图表库，实现医院数据统计图表展示。
7. SCSS/CSS - 用于样式的定义和布局。

**2.4.2后端技术**
1. Spring Boot 3.9.0 - 用于简化新Spring应用的初始搭建以及开发过程的框架。
2. Spring Security - 提供认证和授权功能的安全框架。
3. MyBatis Plus 3.5.7 - 持久层框架，用于操作数据库，将SQL语句与程序代码分离。
4. MySQL - 关系型数据库管理系统，用于存储和管理医院业务数据。
5. JWT - 用于用户身份验证的令牌技术。

**数据库**
1. MySQL - 用于数据存储，处理医院、科室、医生、预约等数据的创建、读取、更新和删除（CRUD）操作。

**2.5**  **系统开发平台及运行环境**

 

**2.5.1**  **系统开发平台**

 

 开发工具和环境
1. Node.js - JavaScript运行环境，常用于运行前端开发工具。
2. Webpack - 前端资源加载/打包工具，Vue CLI项目中默认使用Webpack进行资源的编译打包。
3. Git - 版本控制系统，用于代码的版本管理和团队协作。
4. Visual Studio Code - 编辑器，用于编写代码，支持多种语言和框架，提供插件支持。
5. Postman - API开发工具，用于测试和调试API接口。
 其他技术和库
1. JavaScript (ES6+) - 现代JavaScript语言标准，提供了更多的语言特性支持。
2. HTML5 - 标记语言，用于构建和结构化网页内容。
这些技术的组合为构建复杂的Web应用提供了坚实的基础，使得应用不仅具备良好的用户体验，还能高效地处理数据和逻辑。

**2.5.2**  **运行环境**

 

操作系统：Windows 10。

服务器软件：Tomcat9.0 (SpringBoot 框架自带 Tomcat)。

浏览器：IE 、Fire Fox 、Edge。

 

**第三章** **需求分析**

 

**3.1**  **系统功能模块概述和分析**

 

《RuoYi-Hospital医院管理系统》采用B/S架构，主要针对管理员、医生和患者三类角色。管理员角色用户的功能：登录、管理医院信息、管理科室信息、管理医生信息、管理预约订单、管理排班信息、管理就诊记录、系统配置等功能。医生角色用户有登录、查看个人排班信息、管理患者就诊记录、查看预约信息等功能。患者角色用户有登录、浏览医院信息、浏览科室信息、浏览医生信息、在线预约挂号、查看个人就诊记录等功能。本系统在系统的设计与开发过程中严格遵守软件工程的规范，运用软件设计模式，从而减少系统模块间的耦合，力求做到系统的稳定性、可重用性和可扩充性。

《RuoYi-Hospital医院管理系统》主要功能如下：

① 医院管理：管理员能管理医院基本信息，包括医院名称、等级、地址、联系方式等信息的维护。

② 科室管理：管理员可以管理医院各科室信息，包括科室名称、简介、所属医院等信息。

③ 医生管理：管理员可以管理医生信息，包括医生姓名、职称、擅长领域、挂号费等信息。

④ 预约管理：管理员和医生可以管理患者预约信息，包括预约时间、患者信息、就诊科室等。

⑤ 排班管理：管理员可以管理医生排班信息，包括排班日期、时间段、号源数量等。

⑥ 就诊记录管理：医生可以管理患者就诊记录，包括诊断结果、治疗方案、处方等信息。

**3.1.1** **登录**

 

| 功能描述 | 系统用户（管理员、医生和患者）能对系统进行登录操作           |
| -------- | ------------------------------------------------------------ |
| 输入项   | 用户名、密码                                                 |
| 处理描述 | 系统用户正确输入用户名、密码后，点击登录按钮，能进入对应角色的管理界面 |
| 输出项   | 无                                                           |
| 业务规则 | 不同角色用户登录后进入对应的功能界面                         |
| 界面要求 | 友好、美观、易用                                             |

**3.1.2** **查询医生信息**



| 功能描述 | 系统用户（管理员和患者）能进行查看医生信息的操作             |
| -------- | ------------------------------------------------------------ |
| 输入项   | 医生姓名、科室名称（可选）                                   |
| 处理描述 | 系统用户要先登录系统，登录后可以在医生列表浏览医生信息，同时也能输入医生姓名或科室名称进行搜索医生信息的操作。 |
| 输出项   | 医生详细信息列表                                             |
| 业务规则 | 管理员能看到所有医生信息，患者能浏览医生基本信息用于预约挂号。 |
| 界面要求 | 友好、美观、易用                                             |

**3.1.3** **添加预约信息**



| 功能描述 | 系统用户（患者和管理员）能进行添加预约信息的操作             |
| -------- | ------------------------------------------------------------ |
| 输入项   | 患者姓名、身份证号、医生姓名、预约日期、就诊时段             |
| 处理描述 | 系统用户要先登录系统，在预约管理页面，点击添加按钮，按要求输入预约信息后，点击保存按钮，完成添加预约信息的操作。 |
| 输出项   | 预约成功确认信息                                             |
| 业务规则 | 管理员能进行添加所有预约信息的操作，患者只能进行添加自己的预约信息操作。 |
| 界面要求 | 友好、美观、易用                                             |

**3.1.4** **编辑医生信息**



| 功能描述 | 管理员能进行编辑医生信息的操作                               |
| -------- | ------------------------------------------------------------ |
| 输入项   | 医生姓名、所属科室、职称、擅长领域、从业年限、挂号费         |
| 处理描述 | 管理员要先登录系统，在医生管理页面，选择一条医生数据后，点击编辑按钮，编辑完医生信息后，点击保存按钮，完成编辑医生信息的操作。 |
| 输出项   | 无                                                           |
| 业务规则 | 管理员能进行编辑医生信息的操作                               |
| 界面要求 | 友好、美观、易用                                             |

**3.1.5** **查询统计信息**



| 功能描述 | 系统用户（管理员和医生）能进行查询医院统计信息的操作         |
| -------- | ------------------------------------------------------------ |
| 输入项   | 无                                                           |
| 处理描述 | 系统用户登录系统后，在首页能看到医院预约数量、就诊人数、医生数量、科室数量等统计图表 |
| 输出项   | 统计图表和数据                                               |
| 业务规则 | 管理员能查看所有统计信息，医生能查看相关的业务统计           |
| 界面要求 | 友好、美观、易用                                             |

 

**3.2**  **系统功能模块设计**

 

根据系统功能分析，将整个系统的功能模块规划为如下的功能模块图。

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps1.png) 

 

 

**3.3**  **流程分析**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps2.png)

**3.4**  **用例分析**

 

**、**![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps3.jpg)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps4.png) 

 

 

**3.5**  **数据库分析**

 

信息系统的主要任务是通过大量数据获得管理所需要的信息，这就要求系统本身能够存 储和管理大量的数据，而这一功能的实现必须借助大型数据库系统。本系统的开发选择 MySQL 作为后台数据库开发工具。

**1.**    **概念模型设计**

概念模型用于信息世界的建模，与具体的 DBMS 无关。为了把现实世界中的具体事物 抽象、组织为某一 DBMS 支持的数据模型。人们常常首先将现实世界抽象为信息世界，然 后再将信息世界转换为机器世界。也就是说，首先把现实世界中的客观对象抽象为某一种信 息结构，这种信息结构并不依赖于具体的计算机系统和具体的 DBMS ，而是概念级的模型， 然后再把模型转换为计算机上某一个 DBMS 支持的数据模型。实际上，概念模型是现实世 界到机器世界的一个中间层次。

信息世界中包含的基本概念有实体和联系。

(1)  实体 (entity)

客观存在并可相互区别的事物称为实体。实体可以是具体的人、事、物， 也可以是抽象 的概念或联系。例如，一个学生、一门课、一个供应商、一个部门、一本 书、一位读者等 都是实体。

(2)  联系 (relationship)

在现实世界中，事物内部以及事物之间是有联系的，这些联系在信息世界中反映为实体 内部的联系和实体之间的联系。实体内部的联系通常是组成实体的各属性之间的联系。两个 实体型之间的联系可以分为 3 类，一对一联系，(1:1)；一对多联系(1 : n)；多对多联系(m : n)。

概念模型是对信息世界建模，所以概念模型应该能够方便、准确地表示信息世界中的常 用概念。概念模型的表示方法很多，其中最为常用的是 P.P.S.Chen 于 1976 年提出的实体， 联系方法(Entity-Relationship  Approach)简记为 E-R 表示法)。该方法用 E-R 图来描述现实世 界的概念模型，称为实体-联系模型，简称 E-R 模型。

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps5.png) 

 

 

 

 

 

 

**2.**    **数据库表设计**

数据库表设计主要是把概念结构设计时设计好的基本 E-R 图转换为与选用 DBMS 产品 所支持的数据模型相符合的逻辑结构。它包括数据项、记录及记录间的联系、安全性和一致 性约束等等。导出的逻辑结构是否与概念模式一致，从功能和性能上是否满足用户的要求， 要进行模式评价。

本系统数据库表如下：

**Hospitals** 医院信息表

| 字段名称      | 数据类型     | 主键 | 是否空 | 说明                                    |
| ------------- | ------------ | ---- | ------ | --------------------------------------- |
| hospital_id   | int          | 是   | 否     | 医院ID，自增主键                        |
| hospital_name | varchar(100) | 否   | 否     | 医院名称                                |
| hospital_level| enum         | 否   | 否     | 医院等级（三级甲等、三级乙等等）        |
| address       | varchar(200) | 否   | 否     | 医院地址                                |
| phone         | varchar(20)  | 否   | 是     | 联系电话                                |
| description   | text         | 否   | 是     | 医院简介                                |

**Departments** 科室信息表

| 字段名称     | 数据类型    | 主键 | 是否空 | 说明                     |
| ------------ | ----------- | ---- | ------ | ------------------------ |
| dept_id      | int         | 是   | 否     | 科室ID，自增主键         |
| dept_name    | varchar(50) | 否   | 否     | 科室名称                 |
| hospital_id  | int         | 否   | 否     | 所属医院ID               |
| description  | text        | 否   | 是     | 科室简介                 |

**Doctors** 医生信息表

| 字段名称         | 数据类型     | 主键 | 是否空 | 说明                           |
| ---------------- | ------------ | ---- | ------ | ------------------------------ |
| doctor_id        | int          | 是   | 否     | 医生ID，自增主键               |
| doctor_name      | varchar(20)  | 否   | 否     | 医生姓名                       |
| dept_id          | int          | 否   | 否     | 所属科室ID                     |
| title            | enum         | 否   | 否     | 职称（主任医师、副主任医师等） |
| specialty        | varchar(100) | 否   | 是     | 擅长领域                       |
| work_years       | int          | 否   | 是     | 从业年限                       |
| consultation_fee | decimal(10,2)| 否   | 否     | 挂号费                         |

**Appointments** 预约订单表

| 字段名称       | 数据类型    | 主键 | 是否空 | 说明                 |
| -------------- | ----------- | ---- | ------ | -------------------- |
| appointment_id | varchar(36) | 是   | 否     | 预约ID，UUID生成     |
| patient_name   | varchar(20) | 否   | 是     | 患者姓名             |
| id_card        | varchar(20) | 否   | 是     | 身份证号             |
| doctor_name    | varchar(20) | 否   | 是     | 医生姓名             |
| hospital_name  | varchar(20) | 否   | 是     | 医院名称             |
| dept_name      | varchar(20) | 否   | 是     | 科室名称             |
| date           | varchar(20) | 否   | 是     | 就诊日期             |
| time_slot      | varchar(20) | 否   | 否     | 就诊时段             |

**Schedules** 排班信息表

| 字段名称             | 数据类型    | 主键 | 是否空 | 说明         |
| -------------------- | ----------- | ---- | ------ | ------------ |
| schedule_id          | int         | 是   | 否     | 排班ID       |
| doctor_id            | int         | 否   | 否     | 医生ID       |
| date                 | varchar(20) | 否   | 否     | 排班日期     |
| morning_start        | time        | 否   | 是     | 上午开始时间 |
| morning_end          | time        | 否   | 是     | 上午结束时间 |
| afternoon_start      | time        | 否   | 是     | 下午开始时间 |
| afternoon_end        | time        | 否   | 是     | 下午结束时间 |
| morning_quota        | int         | 否   | 是     | 上午号源数量 |
| afternoon_quota      | int         | 否   | 是     | 下午号源数量 |

**Medical_Records** 就诊记录表

| 字段名称          | 数据类型     | 主键 | 是否空 | 说明           |
| ----------------- | ------------ | ---- | ------ | -------------- |
| record_id         | int          | 是   | 否     | 记录ID         |
| appointment_id    | varchar(32)  | 否   | 否     | 关联预约订单号 |
| patient_name      | int          | 否   | 否     | 患者姓名       |
| doctor_id         | int          | 否   | 否     | 医生ID         |
| diagnosis         | text         | 否   | 是     | 诊断结果       |
| treatment_plan    | text         | 否   | 是     | 治疗方案       |
| prescription      | text         | 否   | 是     | 处方内容       |

**3.6**  **设计类图分析**

系统采用分层架构设计，主要包括控制层（Controller）、服务层（Service）、数据访问层（Mapper）和实体层（Domain）。各层之间职责明确，降低了系统的耦合度。

**3.7**  **交互类图分析**

 

**3.7.1** **顺序图**

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps11.png) 

 

 

 

 

 

 

**3.8**  **系统架构设计图**![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps12.png)

**第四章** **RuoYi-Hospital医院管理系统的设计与实现**

《RuoYi-Hospital医院管理系统》主要针对管理员、医生和患者三类角色。整个系统对待所有用户均采用相同的入口。不同用户的操作选项会因为其所扮演的角色的权限而有所区别，整个系统界面风格采用现代化管理系统的设计风格，简约大气而且方便使用，下面就具体来叙述整个系统的设计和实现。

 

**4.1**    **登录页面设计**

 

下图是用户登录的页面，用户登录需要输入正确的用户名、密码来进行登录。表单分别会进行后端Java验证。验证通过则会根据用户角色登录到对应的管理界面，如果验证未通过则会在页面出现相应的错误提示。

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps13.jpg) 

 

 

 

 

 

 

 

 

 

**4.2**   **系统首页界面**

下图为系统首页界面，可以看到医院数量、科室数量、医生数量、预约数量等关键业务数据的统计图表展示：

**4.3**    **医院管理页面**

如下图为医院管理页面，可以查看和管理医院基本信息，包括医院名称、等级、地址、联系方式等信息的增删改查操作。

**4.4**    **科室管理页面**

如下图为科室管理页面，可以管理医院各科室信息，包括科室名称、所属医院、科室简介等信息的维护。

**4.5**    **医生管理页面**

如下图为医生管理页面，可以管理医生信息，包括医生姓名、所属科室、职称、擅长领域、从业年限、挂号费等详细信息。

**4.6**    **预约管理页面**

如下图是预约管理页面，可以查看和管理患者的预约信息，包括预约时间、患者信息、医生信息、就诊科室等。

**4.7**    **排班管理页面**

如下图是排班管理页面，可以管理医生的排班信息，包括排班日期、时间段、号源数量等信息的设置和调整。

**4.8**    **接****口****设计表**![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml17688\wps19.jpg)

**第五章** **总结**

 

本文阐述了RuoYi-Hospital医院管理系统的开发背景与意义，并对系统进行了全面设计，进而实现了该系统。RuoYi-Hospital医院管理系统可以实现医院对医疗资源和患者信息更好管理的愿望，具有很强的实用性。

通过开发这个系统，我较为全面的掌握了Java的基本知识和编程技巧，特别是SpringBoot框架、Vue前端技术、MyBatis数据库操作等技术栈的应用，并在开发过程中进一步提高了我的Java开发能力，也让我积累了许多宝贵经验：系统分析的好坏将决定着系统开发成功与否，一份好的分析设计将是成功开发的主要因素。我们在着手开发之前不要急于编程，先应有较长的时间去把需求分析做好，做好数据库设计工作，写出相关的开发文档等。然后再开始编写程序代码，这样做到每段代码心底都有数，有条不紊。

总的来说，利用信息化技术来实现医院管理系统是促进医疗服务现代化的必然趋势，本文对系统的开发只是基于个人的一个尝试性的开发，虽然取得了一定的成果，但是由于时间的限制，仍然存在许多的不足之处，比如移动端适配、更多的业务功能模块等，因此需要后期的不断完善和优化。

 

**致谢**

 

在开发文档完成之际，我首先要向尊敬的老师表示最真挚的谢意。

在开发文档写作期间，为了保证我们项目开发的正常进行，学院抽调了优秀的老师指导 我们进行项目开发，并且不时地询问我们项目开发的进展情况。没有郭红亮老师们的细心指 导我的开发文档与系统就不可能顺利的完成，再次对你们表示衷心地感谢。老师认真负责的 工作态度、严谨的治学风格，使我深受启发；同时也很感谢帮助过我和我一同探讨问题的同 学们。为我们这次开发的正常开展提供了必要的基础。本次开发，就要画上一个句号了。

感谢所有关心、支持、帮助过我的良师益友。

 

**参考文献**

 

 

[1]张明华. 现代医院信息化管理系统的构建与实践研究[J].中国医院管理,2023.

[2]李建国. 基于SpringBoot的医院管理信息系统的设计与实现[D].北京理工大学,2022.

[3]王丽娟. 医院信息管理系统的设计与实现[D].大连理工大学,2021.

[4]陈志强. 我国医院信息化建设现状与发展趋势分析[J].中国数字医学,2023.

[5]陈雄华,陈艳.Spring企业级应用开发详解[M].电子工业出版社,2020.

[6]杨静. 基于Java Web中MVC模式的研究与应用[J].电脑知识与技术,2022.

[7]赵利庆. Java Web架构中数据库优化模式的研究与发现[D].北京邮电大学,2021.

[8]喻佳,吴丹新.基于SpringBoot的Web快速开发框架[J].电脑编程技巧与维护,2023.

[9]荣艳冬.关于MyBatis持久层框架的应用研究[J].信息安全与技术,2022.

[10]刘伟.基于B/S模式的医院信息管理系统设计[J].自动化与仪器仪表,2023.

[11]黄梯云,李一军.管理信息系统（第五版）[M].高等教育出版社,2020.

[12]若依开发团队.RuoYi快速开发平台技术文档[EB/OL].http://doc.ruoyi.vip,2023.