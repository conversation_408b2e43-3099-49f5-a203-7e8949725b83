import request from '@/utils/request'

// 查询科室信息列表
export function listDepartments(query) {
  return request({
    url: '/system/departments/list',
    method: 'get',
    params: query
  })
}

// 查询科室信息详细
export function getDepartments(deptId) {
  return request({
    url: '/system/departments/' + deptId,
    method: 'get'
  })
}

// 新增科室信息
export function addDepartments(data) {
  return request({
    url: '/system/departments',
    method: 'post',
    data: data
  })
}

// 修改科室信息
export function updateDepartments(data) {
  return request({
    url: '/system/departments',
    method: 'put',
    data: data
  })
}

// 删除科室信息
export function delDepartments(deptId) {
  return request({
    url: '/system/departments/' + deptId,
    method: 'delete'
  })
}
