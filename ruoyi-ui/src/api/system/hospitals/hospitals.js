import request from '@/utils/request'
import { get } from 'sortablejs'

// 查询医院信息列表
export function listHospitals(query) {
  return request({
    url: '/system/hospitals/list',
    method: 'get',
    params: query
  })
}

// 查询医院信息详细
export function getHospitals(hospitalId) {
  return request({
    url: '/system/hospitals/' + hospitalId,
    method: 'get'
  })
}

// 根据医院名称查询所拥有的科室
export function getDeptName(hospitalName){
  return request({
    url: '/system/hospitals/' + hospitalName,
    method: get
  })
}

// 新增医院信息
export function addHospitals(data) {
  return request({
    url: '/system/hospitals',
    method: 'post',
    data: data
  })
}

// 修改医院信息
export function updateHospitals(data) {
  return request({
    url: '/system/hospitals',
    method: 'put',
    data: data
  })
}

// 删除医院信息
export function delHospitals(hospitalId) {
  return request({
    url: '/system/hospitals/' + hospitalId,
    method: 'delete'
  })
}
