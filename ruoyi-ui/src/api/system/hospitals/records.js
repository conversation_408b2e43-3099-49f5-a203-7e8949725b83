import request from '@/utils/request'

// 查询就诊记录列表
export function listRecords(query) {
  return request({
    url: '/system/records/list',
    method: 'get',
    params: query
  })
}

// 查询就诊记录详细
export function getRecords(recordId) {
  return request({
    url: '/system/records/' + recordId,
    method: 'get'
  })
}

// 新增就诊记录
export function addRecords(data) {
  return request({
    url: '/system/records',
    method: 'post',
    data: data
  })
}

// 修改就诊记录
export function updateRecords(data) {
  return request({
    url: '/system/records',
    method: 'put',
    data: data
  })
}

// 删除就诊记录
export function delRecords(recordId) {
  return request({
    url: '/system/records/' + recordId,
    method: 'delete'
  })
}
