import request from '@/utils/request'

// 查询预约订单列表
export function listAppointments(query) {
  return request({
    url: '/system/appointments/list',
    method: 'get',
    params: query
  })
}

// 查询预约订单详细
export function getAppointments(appointmentId) {
  return request({
    url: '/system/appointments/' + appointmentId,
    method: 'get'
  })
}

// 新增预约订单
export function addAppointments(data) {
  return request({
    url: '/system/appointments',
    method: 'post',
    data: data
  })
}

// 修改预约订单
export function updateAppointments(data) {
  return request({
    url: '/system/appointments',
    method: 'put',
    data: data
  })
}

// 删除预约订单
export function delAppointments(appointmentId) {
  return request({
    url: '/system/appointments/' + appointmentId,
    method: 'delete'
  })
}
