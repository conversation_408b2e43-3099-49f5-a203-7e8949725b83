import request from '@/utils/request'

// 查询医生排班列表
export function listSchedules(query) {
  return request({
    url: '/system/schedules/list',
    method: 'get',
    params: query
  })
}

// 查询医生排班详细
export function getSchedules(scheduleId) {
  return request({
    url: '/system/schedules/' + scheduleId,
    method: 'get'
  })
}

// 新增医生排班
export function addSchedules(data) {
  return request({
    url: '/system/schedules',
    method: 'post',
    data: data
  })
}

// 修改医生排班
export function updateSchedules(data) {
  return request({
    url: '/system/schedules',
    method: 'put',
    data: data
  })
}

// 删除医生排班
export function delSchedules(scheduleId) {
  return request({
    url: '/system/schedules/' + scheduleId,
    method: 'delete'
  })
}
