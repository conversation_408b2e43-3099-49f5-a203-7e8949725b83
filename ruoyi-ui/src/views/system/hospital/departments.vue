<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <!-- <el-form-item label="科室名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入科室名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->


      <el-form-item label="所属科室名称" prop="deptName">
        <el-select
          v-model="queryParams.deptName"
          placeholder="请选择所属科室"
          clearable
          @change="handleQuery"
        >
          <el-option 
            v-for="dept in departmentsList"
            :key="dept.deptName"
            :label="dept.deptName"
            :value="dept.deptName"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="所属医院ID" prop="hospitalId">
        <el-input
          v-model="queryParams.hospitalId"
          placeholder="请输入所属医院ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <el-form-item label="所属医院名称" prop="hospitalName">
        <el-select
          v-model="queryParams.hospitalId"
          placeholder="请选择所属医院"
          clearable
          @change="handleQuery"
        >
          <el-option 
            v-for="hospital in hospitalsList"
            :key="hospital.hospitalId"
            :label="hospital.hospitalName"
            :value="hospital.hospitalId"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="删除标志" prop="deleted">
        <el-input
          v-model="queryParams.deleted"
          placeholder="请输入删除标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:departments:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:departments:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:departments:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:departments:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="departmentsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="部门表" align="center" prop="deptId" />
      <el-table-column label="科室名称" align="center" prop="deptName" />
      <!-- <el-table-column label="所属医院ID" align="center" prop="hospitalId" /> -->
       <el-table-column label="所属医院名称" align="center" prop="hospitalName" />
      <el-table-column label="科室简介" align="center" prop="description" />
      <!-- <el-table-column label="删除标志" align="center" prop="deleted" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:departments:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:departments:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改科室信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="科室名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入科室名称" />
        </el-form-item>
        <!-- <el-form-item label="所属医院ID" prop="hospitalId">
          <el-input v-model="form.hospitalId" placeholder="请输入所属医院ID" />
        </el-form-item> -->
        <el-form-item label="医院名称" prop="hospitalId">
          <el-select 
            v-model="form.hospitalId" 
            placeholder="选择你的医院">
            <el-option 
              v-for="hospital in hospitalsList"
              :key="hospital.hospitalId"
              :label="hospital.hospitalName"
              :value="hospital.hospitalId"
             />
          </el-select>
        </el-form-item>
        <el-form-item label="科室简介" prop="description">
          <editor v-model="form.description" :min-height="192"/>
        </el-form-item>
        <!-- <el-form-item label="删除标志" prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入删除标志" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDepartments, getDepartments, delDepartments, addDepartments, updateDepartments } from "@/api/system/hospitals/departments"
import { listHospitals } from "@/api/system/hospitals/hospitals"

export default {
  name: "Departments",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 科室信息表格数据
      departmentsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptName: null,
        hospitalId: null,
        hospitalName: null,
        description: null,
        deleted: null
      },
      // 表单参数
      form: {},
      hospitalsList: [],
      hospitalLoading: false,
      // 表单校验
      rules: {
        deptName: [
          { required: true, message: "科室名称不能为空", trigger: "blur" }
        ],
        hospitalId: [
          { required: true, message: "所属医院ID不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList(),
    this.fetchHospitalName()
  },
  methods: {


    // 获取医院名称
    async fetchHospitalName(){
      this.hospitalLoading = true;
      try{
        console.log("获取医院名称")
        const response = await listHospitals({});
        console.log("医院名称:", response.rows);
        this.hospitalsList = response.rows
          .filter(item => item.hospitalName) // 过滤 doctorName 为 null 的项
          .map(item => ({
            hospitalId: item.hospitalId,
            hospitalName: item.hospitalName
          }))
        if (!this.hospitalsList.length) {
          this.$message.warning('暂无医院数据');
        }
      } catch (error) {
        this.$message.error('获取医院数据失败');
        console.error('Error fetching hospitals:', error); // 打印详细错误
      } finally {
        this.hospitalLoading = false;
      }
    },


    /** 查询科室信息列表 */
    getList() {
      this.loading = true
      listDepartments(this.queryParams).then(response => {
        this.departmentsList = response.rows
        console.log("查看departmentsList数据", this.departmentsList)
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: null,
        deptName: null,
        hospitalId: null,
        description: null,
        createTime: null,
        updateTime: null,
        deleted: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.deptId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加科室信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const deptId = row.deptId || this.ids
      getDepartments(deptId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改科室信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.deptId != null) {
            updateDepartments(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addDepartments(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const deptIds = row.deptId || this.ids
      this.$modal.confirm('是否确认删除科室信息编号为"' + deptIds + '"的数据项？').then(function() {
        return delDepartments(deptIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/departments/export', {
        ...this.queryParams
      }, `departments_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
