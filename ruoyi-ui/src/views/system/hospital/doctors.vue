<template>
  <div class="app-container">
    <!-- 查询菜单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="所属科室ID" prop="deptId">
        <el-input
          v-model="queryParams.deptId"
          placeholder="请输入所属科室ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <el-form-item label="科室名称" prop="deptId">
          <el-select
          v-model="queryParams.deptId"
          clearable
          placeholder="请输入科室名称"
          @change="handleQuery">
          <el-option
            v-for="dept in deptList"
            :key="dept.deptId"
            :label="dept.deptName"
            :value="dept.deptId"
          />
          </el-select>
        </el-form-item>


      <el-form-item label="擅长领域" prop="specialty">
        <el-input
          v-model="queryParams.specialty"
          placeholder="请输入擅长领域"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="从业年限" prop="workYears">
        <el-input
          v-model="queryParams.workYears"
          placeholder="请输入从业年限"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="挂号费" prop="consultationFee">
        <el-input
          v-model="queryParams.consultationFee"
          placeholder="请输入挂号费"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="删除标志" prop="deleted">
        <el-input
          v-model="queryParams.deleted"
          placeholder="请输入删除标志"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:doctors:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:doctors:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:doctors:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:doctors:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="doctorsList" @selection-change="handleSelectionChange">
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-form-item label="医生简介">
              <span>{{ props.row.introduction }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="55" align="center" />
       <el-table-column label="所属科室名称" align="center" prop="deptName" />
      <el-table-column label="医生姓名" align="center" prop="doctorName" />
      <el-table-column label="职称" align="center" prop="title" />

      <el-table-column label="擅长领域" align="center" prop="specialty" />
      <el-table-column label="从业年限" align="center" prop="workYears" />
      <el-table-column label="挂号费" align="center" prop="consultationFee" />
<!--      <el-table-column label="医生简介" align="center" prop="introduction" />-->
      <el-table-column label="医生简介" align="center">
        <template #default="{row}">
          <el-tooltip :content="row.introduction" placement="top" :disabled="!row.introduction || row.introduction.length <= 8">
            <span>{{ row.introduction && row.introduction.length > 8 ? `${row.introduction.substring(0, 8)}...` : row.introduction }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:doctors:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:doctors:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改医生信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="所属科室ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入所属科室ID" />
        </el-form-item> -->

        <el-form-item label="科室名称" prop="deptId">
          <el-select
          v-model="form.deptId"
          placeholder="请输入科室名称">
            <el-option
              v-for="dept in deptList"
              :key="dept.deptId"
              :label="dept.deptName"
              :value="dept.deptId"
            />
          </el-select>
        </el-form-item>


        <el-form-item label="医生姓名" prop="doctorName">
          <el-input v-model="form.doctorName" placeholder="请输入医生姓名" />
        </el-form-item>
        <el-form-item label="职称" prop="title">
          <el-select v-model="form.title" placeholder="请选择职称">
            <el-option label="住院医师" value="住院医师" />
            <el-option label="主治医师" value="主治医师" />
            <el-option label="副主任医师" value="副主任医师" />
            <el-option label="主任医师" value="主任医师" />
          </el-select>
        </el-form-item>
        <el-form-item label="擅长领域" prop="specialty">
          <el-input v-model="form.specialty" placeholder="请输入擅长领域" />
        </el-form-item>
        <el-form-item label="从业年限" prop="workYears">
          <el-input v-model="form.workYears" placeholder="请输入从业年限" />
        </el-form-item>
        <el-form-item label="挂号费" prop="consultationFee">
          <el-input v-model="form.consultationFee" placeholder="请输入挂号费" />
        </el-form-item>
        <el-form-item label="医生简介" prop="introduction">
          <el-input v-model="form.introduction" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <!-- <el-form-item label="删除标志" prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入删除标志" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDoctors, getDoctors, delDoctors, addDoctors, updateDoctors } from "@/api/system/hospitals/doctors"
import {listDepartments} from "@/api/system/hospitals/departments"

export default {
  name: "Doctors",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 医生信息表格数据
      doctorsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        title: null,
        specialty: null,
        workYears: null,
        consultationFee: null,
        introduction: null,
        deleted: null,
        deptName: null,
      },
      // 表单参数
      form: {},
      deptList: [],
      deptLoading: false,
      // 表单校验
      rules: {
        deptId: [
          { required: true, message: "所属科室ID不能为空", trigger: "blur" }
        ],
        title: [
          { required: true, message: "职称不能为空", trigger: "blur" }
        ],
        consultationFee: [
          { required: true, message: "挂号费不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },

  mounted(){
    this.fetchDepts();
  },

  methods: {


    // 获取科室信息
    async fetchDepts() {
      this.deptLoading = true;
      try {
        console.log("Fetching 科室...");
        const response = await listDepartments({});
        console.log("科室：", response.rows); // 改进日志输出
        this.deptList = response.rows
          .map(item => ({
            deptId: item.deptId,
            deptName: item.deptName,
          }));
        if (!this.deptList.length) {
          this.$message.warning('暂无科室数据');
        }
      } catch (error) {
        this.$message.error('获取科室数据失败');
        console.error('Error fetching department:', error); // 打印详细错误
      } finally {
        this.deptLoading = false;
      }
    },


    /** 查询医生信息列表 */
    getList() {
      this.loading = true
      listDoctors(this.queryParams).then(response => {
        this.doctorsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        doctorId: null,
        deptId: null,
        title: null,
        specialty: null,
        workYears: null,
        consultationFee: null,
        introduction: null,
        createTime: null,
        updateTime: null,
        deleted: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.doctorId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加医生信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const doctorId = row.doctorId || this.ids
      getDoctors(doctorId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改医生信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.doctorId != null) {
            updateDoctors(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addDoctors(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const doctorIds = row.doctorId || this.ids
      this.$modal.confirm('是否确认删除医生信息编号为"' + doctorIds + '"的数据项？').then(function() {
        return delDoctors(doctorIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/doctors/export', {
        ...this.queryParams
      }, `doctors_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
